/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';
import { POST } from '../route';

// Mock the dependencies
jest.mock('@/services/db', () => ({
  db: {
    workspaceInvite: {
      findFirst: jest.fn(),
      update: jest.fn(),
    },
    workspaceMembership: {
      findFirst: jest.fn(),
      create: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

jest.mock('@/services/firebase/server-app', () => ({
  getAuthenticatedAppForUser: jest.fn(),
}));

const mockDb = db as jest.Mocked<typeof db>;
const mockGetAuthenticatedAppForUser = getAuthenticatedAppForUser as jest.MockedFunction<
  typeof getAuthenticatedAppForUser
>;

describe('/api/invites/token/[token]/accept - POST', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockCurrentUser = {
    uid: 'user-1',
    email: '<EMAIL>',
  };

  const mockInvitation = {
    id: 'invite-1',
    email: '<EMAIL>',
    status: 'PENDING',
    workspaceId: 'workspace-1',
    roleId: 'role-1',
    workspace: {
      id: 'workspace-1',
      name: 'Test Workspace',
    },
    role: {
      id: 'role-1',
      name: 'Member',
    },
  };

  const mockMembership = {
    id: 'membership-1',
    userId: 'user-1',
    workspaceId: 'workspace-1',
    roleId: 'role-1',
    role: {
      id: 'role-1',
      name: 'Member',
    },
  };

  it('should accept invitation successfully', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockGetAuthenticatedAppForUser.mockResolvedValue({ currentUser: mockCurrentUser } as any);
    mockDb.workspaceInvite.findFirst.mockResolvedValue(mockInvitation as any);
    mockDb.workspaceMembership.findFirst.mockResolvedValue(null); // No existing membership
    mockDb.$transaction.mockImplementation(async (callback) => {
      return await callback({
        workspaceMembership: {
          create: jest.fn().mockResolvedValue(mockMembership),
        },
        workspaceInvite: {
          update: jest.fn().mockResolvedValue(mockInvitation),
        },
      });
    });

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/accept', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.message).toBe('Invitation accepted successfully');
    expect(data.workspace).toEqual(mockInvitation.workspace);
    expect(data.membership).toEqual(mockMembership);
  });

  it('should return 400 for invalid token format', async () => {
    const invalidToken = 'invalid-token';

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/accept', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: invalidToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe('Invalid token format');
    expect(mockGetAuthenticatedAppForUser).not.toHaveBeenCalled();
  });

  it('should return 401 for unauthenticated user', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockGetAuthenticatedAppForUser.mockResolvedValue({ currentUser: null } as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/accept', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(401);
    expect(data.error).toBe('Authentication required');
  });

  it('should return 404 for non-existent invitation', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockGetAuthenticatedAppForUser.mockResolvedValue({ currentUser: mockCurrentUser } as any);
    mockDb.workspaceInvite.findFirst.mockResolvedValue(null);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/accept', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(404);
    expect(data.error).toBe('Invitation not found or expired');
  });

  it('should return 410 for already processed invitation', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    const processedInvitation = { ...mockInvitation, status: 'ACCEPTED' };

    mockGetAuthenticatedAppForUser.mockResolvedValue({ currentUser: mockCurrentUser } as any);
    mockDb.workspaceInvite.findFirst.mockResolvedValue(processedInvitation as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/accept', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(410);
    expect(data.error).toBe('Invitation already processed');
    expect(data.status).toBe('ACCEPTED');
  });

  it('should return 403 for email mismatch', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
    const mismatchInvitation = { ...mockInvitation, email: '<EMAIL>' };

    mockGetAuthenticatedAppForUser.mockResolvedValue({ currentUser: mockCurrentUser } as any);
    mockDb.workspaceInvite.findFirst.mockResolvedValue(mismatchInvitation as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/accept', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(403);
    expect(data.error).toBe('Email mismatch - this invitation is for a different email address');
  });

  it('should return 400 for user already member', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockGetAuthenticatedAppForUser.mockResolvedValue({ currentUser: mockCurrentUser } as any);
    mockDb.workspaceInvite.findFirst.mockResolvedValue(mockInvitation as any);
    mockDb.workspaceMembership.findFirst.mockResolvedValue(mockMembership as any); // Existing membership
    mockDb.workspaceInvite.update.mockResolvedValue(mockInvitation as any);

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/accept', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.error).toBe('You are already a member of this workspace');
    expect(mockDb.workspaceInvite.update).toHaveBeenCalledWith({
      where: { id: mockInvitation.id },
      data: { status: 'ACCEPTED' },
    });
  });

  it('should handle database errors', async () => {
    const validToken = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

    mockGetAuthenticatedAppForUser.mockResolvedValue({ currentUser: mockCurrentUser } as any);
    mockDb.workspaceInvite.findFirst.mockRejectedValue(new Error('Database error'));

    const request = new NextRequest('http://localhost:3000/api/invites/token/test/accept', {
      method: 'POST',
    });
    const params = Promise.resolve({ token: validToken });

    const response = await POST(request, { params });
    const data = await response.json();

    expect(response.status).toBe(500);
    expect(data.error).toBe('Failed to accept invitation');
  });
});
