import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

/**
 * @swagger
 * /api/workspace/{workspaceId}/roles:
 *   get:
 *     summary: Get workspace roles
 *     description: Retrieve all roles available for a specific workspace. Only workspace members can access this endpoint.
 *     tags:
 *       - Workspace
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The workspace ID
 *     responses:
 *       200:
 *         description: Successfully retrieved workspace roles
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   workspaceId:
 *                     type: string
 *                     nullable: true
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized"
 *       403:
 *         description: Forbidden - user is not a member of the workspace
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "You do not have access to this workspace"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to get workspace roles"
 */
export async function GET(request: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  const params = await props.params;
  try {
    const { workspaceId } = params;

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Check if the user is a member of the workspace
    const userMembership = await db.workspaceMembership.findFirst({
      where: {
        userId: currentUser.uid,
        workspaceId: workspaceId,
      },
    });

    if (!userMembership) {
      return NextResponse.json({ error: 'You do not have access to this workspace' }, { status: 403 });
    }

    // Get all roles available for this workspace (workspace-specific + global roles)
    const roles = await db.role.findMany({
      where: {
        OR: [
          { workspaceId: workspaceId },
          { workspaceId: null }, // Global roles
        ],
      },
      select: {
        id: true,
        name: true,
        workspaceId: true,
      },
      orderBy: [
        { workspaceId: 'asc' }, // Global roles first
        { name: 'asc' },
      ],
    });

    return NextResponse.json(roles);
  } catch (error) {
    console.error('Error getting workspace roles:', error);
    return NextResponse.json({ error: 'Failed to get workspace roles' }, { status: 500 });
  }
}
