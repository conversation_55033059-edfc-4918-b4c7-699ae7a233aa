'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
// Import API service for resetting on logout
import { resetClientApiService } from '@/services/api/client';
import { deleteCookie, setCookie } from 'cookies-next';
import {
  createUserWithEmailAndPassword,
  EmailAuthProvider,
  GithubAuthProvider,
  GoogleAuthProvider,
  onAuthStateChanged,
  onIdTokenChanged,
  reauthenticateWithCredential,
  sendPasswordResetEmail,
  signInWithEmailAndPassword,
  signInWithPopup,
  signOut,
  updatePassword,
  User,
  UserCredential,
} from 'firebase/auth';

// Import Firebase client initialization
import { auth } from '@/services/firebase/client-app';

// Auth providers
const googleProvider = new GoogleAuthProvider();
const githubProvider = new GithubAuthProvider();

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  signInWithEmail: (_email: string, _password: string) => Promise<UserCredential>;
  signInWithGoogle: () => Promise<UserCredential>;
  signInWithGithub: () => Promise<UserCredential>;
  signUpWithEmail: (_email: string, _password: string) => Promise<UserCredential>;
  resetPassword: (_email: string) => Promise<void>;
  updatePassword: (_currentPassword: string, _newPassword: string) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
}

// Default context with not implemented methods
const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  error: null,
  signInWithEmail: () => Promise.reject(new Error('Not implemented')),
  signInWithGoogle: () => Promise.reject(new Error('Not implemented')),
  signInWithGithub: () => Promise.reject(new Error('Not implemented')),
  signUpWithEmail: () => Promise.reject(new Error('Not implemented')),
  resetPassword: () => Promise.reject(new Error('Not implemented')),
  updatePassword: () => Promise.reject(new Error('Not implemented')),
  logout: () => Promise.reject(new Error('Not implemented')),
  clearError: () => {},
} as AuthContextType);

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{
  children: React.ReactNode;
  initialUser?: User | null;
}> = ({ children, initialUser = null }) => {
  const [user, setUser] = useState<User | null>(initialUser);
  const [loading, setLoading] = useState(!initialUser);
  const [error, setError] = useState<string | null>(null);

  // Listen for auth state changes and update the user state
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (authUser) => {
      if (!authUser) {
        deleteCookie('__session', { path: '/' });
      }

      setUser(authUser);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Listen for ID token changes and update the session cookie
  useEffect(() => {
    const unsubscribe = onIdTokenChanged(auth, async (authUser) => {
      if (authUser) {
        const idToken = await authUser.getIdTokenResult();
        const expirationTime = new Date(idToken.expirationTime).getTime();
        const expirationSeconds = Math.floor((expirationTime - Date.now()) / 1000);

        setCookie('__session', idToken.token, {
          path: '/',
          maxAge: expirationSeconds,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
        });
      } else {
        deleteCookie('__session', { path: '/' });
      }
    });

    return () => unsubscribe();
  }, []);

  // Force refresh the token every 50 minutes (tokens expire after 60 minutes)
  // TODO: is this needed?
  /*   useEffect(() => {
    const handle = setInterval(
      async () => {
        const currentUser = auth.currentUser;

        if (currentUser) {
          // Force token refresh
          await currentUser.getIdToken(true);
        }
      },
      50 * 60 * 1000
    ); // 50 minutes

    // Clean up interval on unmount
    return () => clearInterval(handle);
  }, []); */

  // Define auth methods directly with error handling
  const signInWithEmail = async (email: string, password: string): Promise<UserCredential> => {
    try {
      setError(null);
      return await signInWithEmailAndPassword(auth, email, password);
    } catch (err) {
      const firebaseError = err as { code?: string; message: string };
      const errorMessage = firebaseError.code || firebaseError.message;
      setError(errorMessage);
      throw err;
    }
  };

  const signInWithGoogle = async (): Promise<UserCredential> => {
    try {
      setError(null);
      return await signInWithPopup(auth, googleProvider);
    } catch (err) {
      const firebaseError = err as { code?: string; message: string };
      const errorMessage = firebaseError.code || firebaseError.message;
      setError(errorMessage);
      throw err;
    }
  };

  const signInWithGithub = async (): Promise<UserCredential> => {
    try {
      setError(null);
      return await signInWithPopup(auth, githubProvider);
    } catch (err) {
      const firebaseError = err as { code?: string; message: string };
      const errorMessage = firebaseError.code || firebaseError.message;
      setError(errorMessage);
      throw err;
    }
  };

  const signUpWithEmail = async (email: string, password: string): Promise<UserCredential> => {
    try {
      setError(null);
      return await createUserWithEmailAndPassword(auth, email, password);
    } catch (err) {
      const firebaseError = err as { code?: string; message: string };
      const errorMessage = firebaseError.code || firebaseError.message;
      setError(errorMessage);
      throw err;
    }
  };

  const resetPassword = async (email: string): Promise<void> => {
    try {
      setError(null);
      await sendPasswordResetEmail(auth, email);
    } catch (err) {
      const firebaseError = err as { code?: string; message: string };
      const errorMessage = firebaseError.code || firebaseError.message;
      setError(errorMessage);
      throw err;
    }
  };

  const updateUserPassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    const currentUser = auth.currentUser;

    if (!currentUser || !currentUser.email) {
      throw new Error('No authenticated user found');
    }

    // Re-authenticate the user with their current password
    const credential = EmailAuthProvider.credential(currentUser.email, currentPassword);
    await reauthenticateWithCredential(currentUser, credential);

    // Update the password
    await updatePassword(currentUser, newPassword);
  };

  const logout = async (): Promise<void> => {
    try {
      setError(null);

      // Reset the API service singleton to ensure a fresh instance is created for the next user
      resetClientApiService();

      // Sign out from Firebase
      await signOut(auth);
    } catch (err) {
      const firebaseError = err as { code?: string; message: string };
      const errorMessage = firebaseError.code || firebaseError.message;
      setError(errorMessage);
      throw err;
    }
  };

  // Function to clear any authentication errors
  const clearError = () => {
    setError(null);
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        error,
        signInWithEmail,
        signInWithGoogle,
        signInWithGithub,
        signUpWithEmail,
        resetPassword,
        updatePassword: updateUserPassword,
        logout,
        clearError,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
