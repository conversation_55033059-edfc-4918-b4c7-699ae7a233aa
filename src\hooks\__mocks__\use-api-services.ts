// Mock implementation of use-api-services hook

export const mockIntegrationApiService = {
  createState: jest.fn(),
  get: jest.fn(),
  uninstall: jest.fn(),
  setStatus: jest.fn(),
};

export const mockUserApiService = {
  getCurrentUser: jest.fn(),
  completeOnboarding: jest.fn(),
  updateCurrentUser: jest.fn(),
  createAvatarPresignedUrl: jest.fn(),
};

export const mockWorkspaceApiService = {
  getWorkspaces: jest.fn(),
  getWorkspace: jest.fn(),
  createWorkspace: jest.fn(),
  updateWorkspace: jest.fn(),
  deleteWorkspace: jest.fn(),
  getWorkspaceMemberships: jest.fn(),
  selectWorkspace: jest.fn(),
  getSelectedWorkspace: jest.fn(),
  getWorkspaceMembers: jest.fn(),
  createInvitation: jest.fn(),
  getWorkspaceInvitations: jest.fn(),
  acceptInvitation: jest.fn(),
  declineInvitation: jest.fn(),
  cancelInvitation: jest.fn(),
  getUserInvitations: jest.fn(),
};

export const mockApiService = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn(),
  request: jest.fn(),
};

export const mockApiServices = {
  apiService: mockApiService,
  userApiService: mockUserApiService,
  workspaceApiService: mockWorkspaceApiService,
  integrationApiService: mockIntegrationApiService,
};

export const useApiServices = () => mockApiServices;
