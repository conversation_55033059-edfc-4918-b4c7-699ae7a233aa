'use client';

import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import FormControl from '@mui/material/FormControl';
import FormHelperText from '@mui/material/FormHelperText';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import TextField from '@mui/material/TextField';

import { Role } from '@prisma/client';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { z as zod } from 'zod';

import { zodResolver } from '@hookform/resolvers/zod';

// Form validation schema
const inviteSchema = zod.object({
  email: zod.string().email('Invalid email address'),
  roleId: zod.string().min(1, 'Role is required'),
});

type InviteFormData = zod.infer<typeof inviteSchema>;

interface InviteMemberDialogProps {
  open: boolean;
  onClose: () => void;
  onInvite: (email: string, roleId: string) => Promise<void>;
  roles: Role[];
  loading?: boolean;
}

export function InviteMemberDialog({ open, onClose, onInvite, roles, loading = false }: InviteMemberDialogProps) {
  const t = useTranslations('settings.team');

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<InviteFormData>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      email: '',
      roleId: '',
    },
  });

  const handleClose = () => {
    if (!isSubmitting) {
      reset();
      onClose();
    }
  };

  const onSubmit = async (data: InviteFormData) => {
    try {
      await onInvite(data.email, data.roleId);
      reset();
      onClose();
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Error inviting member:', error);
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>{t('inviteMember')}</DialogTitle>
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label={t('email')}
                type="email"
                fullWidth
                margin="normal"
                error={!!errors.email}
                helperText={errors.email?.message}
                disabled={isSubmitting}
                autoFocus
              />
            )}
          />

          <Controller
            name="roleId"
            control={control}
            render={({ field }) => (
              <FormControl fullWidth margin="normal" error={!!errors.roleId}>
                <InputLabel>{t('role')}</InputLabel>
                <Select
                  {...field}
                  label={t('role')}
                  disabled={isSubmitting || loading}
                >
                  {roles.map((role) => (
                    <MenuItem key={role.id} value={role.id}>
                      {role.name || role.id}
                    </MenuItem>
                  ))}
                </Select>
                {errors.roleId && <FormHelperText>{errors.roleId.message}</FormHelperText>}
              </FormControl>
            )}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} disabled={isSubmitting}>
            {t('cancel')}
          </Button>
          <Button type="submit" variant="contained" disabled={isSubmitting}>
            {isSubmitting ? t('inviting') : t('sendInvite')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}
