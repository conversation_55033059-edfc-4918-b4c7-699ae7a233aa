# name: bms-tech-pulse

services:
  # Application
  application:
    build:
      dockerfile: Dockerfile
    restart: always
    ports:
      - '3000:3000'
    environment:
      - NODE_ENV=${NODE_ENV:-development}
    env_file:
      - required: false
        path: .env
      - required: false
        path: .env.local
    networks:
      - bms-network

  # TimescaleDB (PostgreSQL with TimescaleDB extension)
  timescaledb:
    image: timescale/timescaledb:latest-pg17
    restart: always
    ports:
      - '5432:5432'
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${DB_PASSWORD:-postgres}
      - POSTGRES_DB=bms-tech-pulse
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5
    env_file:
      - required: false
        path: ./database/.env
      - required: false
        path: ./database/.env.local
    networks:
      - bms-network

  # Redis for caching
  redis:
    image: redis:alpine
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis}
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - bms-network

  # Nginx for reverse proxy
  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - '80:80'
      - '443:443'
    volumes:
      - ./nginx/conf:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/www:/var/www/html
    depends_on:
      - application
    networks:
      - bms-network

  # Prisma Studio
  prisma-studio:
    image: node:lts-alpine3.17
    working_dir: /usr/src/app
    volumes:
      - .:/usr/src/app
    command: npx prisma studio --port 5555 --browser none
    ports:
      - "5555:5555"
    environment:
      DATABASE_URL: ***********************************************/bms-tech-pulse
    env_file:
      - required: false
        path: .env
      - required: false
        path: .env.local
    depends_on:
      timescaledb:
        condition: service_healthy
    networks:
      - bms-network

networks:
  bms-network:
    driver: bridge

volumes:
  timescaledb_data:
  redis_data:
