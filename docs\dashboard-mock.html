<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pulse Dashboard - Midnight + Electric-Blue Theme</title>
    <style>
        /* Midnight + Electric-Blue Design System */
        :root {
            --bg-primary: #0D1117;
            --bg-surface: #161B22;
            --border-subtle: #30363D;
            --text-primary: #F8FAFC;
            --text-muted: #94A3B8;
            --accent-primary: #3B82F6;
            --accent-hover: #2563EB;
            --accent-subtle: #1E40AF;
            --success: #22C55E;
            --warning: #FACC15;
            --danger: #EF4444;
            --info: #0EA5E9;
            --shadow-card: 0 2px 6px rgba(0, 0, 0, 0.4);
            --shadow-card-hover: 0 4px 12px rgba(0, 0, 0, 0.5);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.5;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 280px 1fr;
            min-height: 100vh;
        }

        .sidebar {
            background-color: var(--bg-surface);
            border-right: 1px solid var(--border-subtle);
            padding: 24px;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            color: var(--accent-primary);
            margin-bottom: 32px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-radius: 12px;
            color: var(--text-muted);
            text-decoration: none;
            margin-bottom: 8px;
            transition: all 0.2s ease;
        }

        .nav-item:hover {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--text-primary);
        }

        .nav-item.active {
            background-color: var(--accent-primary);
            color: var(--text-primary);
        }

        .main-content {
            padding: 32px;
            overflow-y: auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .button {
            padding: 12px 24px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .button-primary {
            background-color: var(--accent-primary);
            color: var(--text-primary);
        }

        .button-primary:hover {
            background-color: var(--accent-hover);
            box-shadow: var(--shadow-card-hover);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .metric-card {
            background-color: var(--bg-surface);
            border: 1px solid var(--border-subtle);
            border-radius: 12px;
            padding: 24px;
            box-shadow: var(--shadow-card);
            transition: all 0.2s ease;
        }

        .metric-card:hover {
            box-shadow: var(--shadow-card-hover);
            transform: translateY(-2px);
        }

        .metric-title {
            color: var(--text-muted);
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .metric-change {
            font-size: 14px;
            font-weight: 500;
        }

        .metric-change.positive {
            color: var(--success);
        }

        .metric-change.negative {
            color: var(--danger);
        }

        .chart-container {
            background-color: var(--bg-surface);
            border: 1px solid var(--border-subtle);
            border-radius: 12px;
            padding: 24px;
            box-shadow: var(--shadow-card);
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, var(--accent-subtle) 0%, var(--accent-primary) 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
            font-weight: 500;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background-color: var(--success); }
        .status-warning { background-color: var(--warning); }
        .status-danger { background-color: var(--danger); }
        .status-info { background-color: var(--info); }
    </style>
</head>
<body>
    <div class="dashboard">
        <aside class="sidebar">
            <div class="logo">🚀 Pulse</div>
            <nav>
                <a href="#" class="nav-item active">📊 Dashboard</a>
                <a href="#" class="nav-item">📈 Analytics</a>
                <a href="#" class="nav-item">👥 Team</a>
                <a href="#" class="nav-item">🔧 Settings</a>
                <a href="#" class="nav-item">💡 Insights</a>
            </nav>
        </aside>

        <main class="main-content">
            <header class="header">
                <h1>Development Analytics</h1>
                <button class="button button-primary">Generate Report</button>
            </header>

            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-title">Total Commits</div>
                    <div class="metric-value">1,247</div>
                    <div class="metric-change positive">+12.5% from last month</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Pull Requests</div>
                    <div class="metric-value">89</div>
                    <div class="metric-change positive">+8.3% from last month</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Code Quality Score</div>
                    <div class="metric-value">94.2%</div>
                    <div class="metric-change negative">-2.1% from last month</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">Team Velocity</div>
                    <div class="metric-value">42</div>
                    <div class="metric-change positive">+15.7% from last month</div>
                </div>
            </div>

            <div class="chart-container">
                <h2 class="chart-title">Commit Activity (Last 30 Days)</h2>
                <div class="chart-placeholder">
                    Interactive Chart Visualization
                </div>
            </div>
        </main>
    </div>
</body>
</html>
