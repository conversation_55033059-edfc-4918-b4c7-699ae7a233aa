import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { isValidInvitationToken } from '@/lib/utils/token';
import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

/**
 * @swagger
 * /api/invites/token/{token}/decline:
 *   post:
 *     summary: Decline invitation by token
 *     description: Decline a workspace invitation using the token from email links. User must be authenticated.
 *     tags:
 *       - Invitations
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: The invitation token
 *     responses:
 *       200:
 *         description: Invitation declined successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 workspace:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *       400:
 *         description: Invalid token format or bad request
 *       401:
 *         description: Unauthorized - invalid or missing authentication
 *       403:
 *         description: Forbidden - email mismatch or other access issue
 *       404:
 *         description: Invitation not found or expired
 *       410:
 *         description: Invitation already accepted or rejected
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
): Promise<NextResponse> {
  try {
    const { token } = await params;

    // Validate token format
    if (!isValidInvitationToken(token)) {
      return NextResponse.json({ error: 'Invalid token format' }, { status: 400 });
    }

    // Get authenticated user
    const { firebaseServerApp, currentUser: currentUser } = await getAuthenticatedAppForUser();
    if (!firebaseServerApp || !currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find the invitation by token
    const invitation = await db.workspaceInvite.findFirst({
      where: {
        token: token,
      },
      include: {
        workspace: {
          select: {
            id: true,
            name: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!invitation) {
      return NextResponse.json({ error: 'Invitation not found or expired' }, { status: 404 });
    }

    // Check if invitation is still pending
    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        {
          error: 'Invitation already processed',
          status: invitation.status,
        },
        { status: 410 }
      );
    }

    // Verify that the authenticated user's email matches the invitation email
    if (currentUser.email !== invitation.email) {
      return NextResponse.json(
        {
          error: 'Email mismatch',
          message: 'This invitation is for a different email address',
        },
        { status: 403 }
      );
    }

    // Update invitation status to REJECTED
    await db.workspaceInvite.update({
      where: {
        id: invitation.id,
      },
      data: {
        status: 'REJECTED',
        updatedAt: new Date(),
      },
    });

    // Return success response
    return NextResponse.json(
      {
        message: 'Invitation declined successfully',
        workspace: invitation.workspace,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error declining invitation by token:', error);
    return NextResponse.json({ error: 'Failed to decline invitation' }, { status: 500 });
  }
}
