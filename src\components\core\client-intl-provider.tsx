'use client';

import { Locale, NextIntlClientProvider } from 'next-intl';
import { ReactNode, useEffect, useState } from 'react';

import { useCurrentUser } from '@/contexts/user-context';

interface ClientIntlProviderProps {
  children: ReactNode;
  messages: any;
  locale: Locale;
  timeZone?: string;
}

export function ClientIntlProvider({
  children,
  messages,
  locale,
  timeZone: fallbackTimeZone,
}: ClientIntlProviderProps) {
  const { user, loading } = useCurrentUser();
  const [currentTimeZone, setCurrentTimeZone] = useState<string>(fallbackTimeZone || 'UTC');

  // Update timezone when user data is loaded or changes
  useEffect(() => {
    console.log("[ClientIntlProvider] useEffect - loading:", loading, "user timezone:", user?.timezone, "fallback:", fallbackTimeZone);
    if (!loading) {
      const timezone = user?.timezone || fallbackTimeZone || 'UTC';
      console.log("[ClientIntlProvider] Setting timezone to:", timezone);
      setCurrentTimeZone(timezone);
    }
  }, [user?.timezone, fallbackTimeZone, loading]);

  console.log('[ClientIntlProvider] Rendered with currentTimeZone:', currentTimeZone, 'user:', user?.id);

  return (
    <NextIntlClientProvider messages={messages} locale={locale} timeZone={currentTimeZone}>
      {children}
    </NextIntlClientProvider>
  );
}
