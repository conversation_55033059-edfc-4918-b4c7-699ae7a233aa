import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { isValidInvitationToken } from '@/lib/utils/token';
import { db } from '@/services/db';

/**
 * @swagger
 * /api/invites/token/{token}:
 *   get:
 *     summary: Get invitation details by token
 *     description: Retrieve invitation details using the invitation token from email links
 *     tags:
 *       - Invitations
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: The invitation token
 *     responses:
 *       200:
 *         description: Invitation details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 email:
 *                   type: string
 *                 status:
 *                   type: string
 *                   enum: [PENDING, ACCEPTED, REJECTED]
 *                 workspace:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *                 role:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                 invitedByUser:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     email:
 *                       type: string
 *                     displayName:
 *                       type: string
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Invalid token format
 *       404:
 *         description: Invitation not found or expired
 *       410:
 *         description: Invitation already accepted or rejected
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
): Promise<NextResponse> {
  try {
    const { token } = await params;

    // Validate token format
    if (!isValidInvitationToken(token)) {
      return NextResponse.json({ error: 'Invalid token format' }, { status: 400 });
    }

    // Find the invitation by token
    const invitation = await db.workspaceInvite.findFirst({
      where: {
        token: token,
      },
      include: {
        workspace: {
          select: {
            id: true,
            name: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
          },
        },
        invitedByUser: {
          select: {
            id: true,
            email: true,
            displayName: true,
          },
        },
      },
    });

    if (!invitation) {
      return NextResponse.json({ error: 'Invitation not found or expired' }, { status: 404 });
    }

    // Check if invitation is still pending
    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        {
          error: 'Invitation already processed',
          status: invitation.status,
        },
        { status: 410 }
      );
    }

    // Return invitation details (without sensitive information)
    const response = {
      id: invitation.id,
      email: invitation.email,
      status: invitation.status,
      workspace: invitation.workspace,
      role: invitation.role,
      invitedByUser: invitation.invitedByUser,
      createdAt: invitation.createdAt,
    };

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Error retrieving invitation by token:', error);
    return NextResponse.json({ error: 'Failed to retrieve invitation' }, { status: 500 });
  }
}
