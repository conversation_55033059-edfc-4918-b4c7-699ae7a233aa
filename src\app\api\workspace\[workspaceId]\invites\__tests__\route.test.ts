/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

import { GET, POST } from '../route';

// Mock dependencies
jest.mock('@/services/firebase/admin', () => ({
  adminAuth: {
    verifyIdToken: jest.fn(),
  },
}));

jest.mock('@/services/db', () => ({
  db: {
    workspaceMembership: {
      findFirst: jest.fn(),
    },
    workspaceInvite: {
      findMany: jest.fn(),
      findFirst: jest.fn(),
      create: jest.fn(),
    },
    user: {
      findFirst: jest.fn(),
    },
    role: {
      findFirst: jest.fn(),
    },
  },
}));

jest.mock('@/services/firebase/server-app', () => ({
  getAuthenticatedAppForUser: jest.fn(),
}));

// Import mocked modules
import { db } from '@/services/db';
import { adminAuth } from '@/services/firebase/admin';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

const _mockAdminAuth = adminAuth as jest.Mocked<typeof adminAuth>;
const mockDb = db as jest.Mocked<typeof db>;
const mockGetAuthenticatedAppForUser = getAuthenticatedAppForUser as jest.MockedFunction<
  typeof getAuthenticatedAppForUser
>;

// Helper function to create mock request
function createMockRequest(method: string, body: any, token?: string): NextRequest {
  const url = 'http://localhost:3000/api/workspace/workspace-1/invites';
  const headers = new Headers();
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }
  headers.set('Content-Type', 'application/json');

  return new NextRequest(url, {
    method,
    headers,
    body: body ? JSON.stringify(body) : undefined,
  });
}

// Mock data
const mockUser = {
  uid: 'user-1',
  email: '<EMAIL>',
  displayName: 'Test User',
};

const mockWorkspaceMembership = {
  id: 'membership-1',
  userId: 'user-1',
  workspaceId: 'workspace-1',
  roleId: 'admin',
  role: {
    id: 'admin',
    name: 'Admin',
    permissions: [{ permission: { id: 'INVITE_MEMBER' } }],
  },
};

const mockInvitation = {
  id: 'invite-1',
  email: '<EMAIL>',
  workspaceId: 'workspace-1',
  invitedBy: 'user-1',
  userId: null,
  roleId: 'member',
  status: 'PENDING',
  createdAt: new Date(),
  updatedAt: new Date(),
  token: null,
  invitedByUser: mockUser,
  user: null,
  role: { id: 'member', name: 'Member' },
};

const mockRole = {
  id: 'member',
  name: 'Member',
  workspaceId: 'workspace-1',
};

describe('/api/workspace/[workspaceId]/invites', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET', () => {
    it('should return workspace invitations for authorized user', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(mockWorkspaceMembership as any);
      mockDb.workspaceInvite.findMany.mockResolvedValue([mockInvitation] as any);

      const request = createMockRequest('GET', null, 'valid-token');
      const response = await GET(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual([
        {
          ...mockInvitation,
          createdAt: mockInvitation.createdAt.toISOString(),
          updatedAt: mockInvitation.updatedAt.toISOString(),
        },
      ]);
      expect(mockDb.workspaceInvite.findMany).toHaveBeenCalledWith({
        where: { workspaceId: 'workspace-1' },
        include: {
          invitedByUser: {
            select: {
              id: true,
              email: true,
              displayName: true,
            },
          },
          user: {
            select: {
              id: true,
              email: true,
              displayName: true,
            },
          },
          role: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });
    });

    it('should return 401 for unauthenticated user', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue(null as any);

      const request = createMockRequest('GET', null);
      const response = await GET(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(401);
    });

    it('should return 403 for user without permission', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(null);

      const request = createMockRequest('GET', null, 'valid-token');
      const response = await GET(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(403);
    });
  });

  describe('POST', () => {
    const validInviteData = {
      email: '<EMAIL>',
      roleId: 'member',
    };

    it('should create invitation for new user', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      // First call: permission check (should return membership)
      // Second call: existing membership check for invited user (should return null)
      mockDb.workspaceMembership.findFirst
        .mockResolvedValueOnce(mockWorkspaceMembership as any) // Permission check
        .mockResolvedValueOnce(null); // No existing membership for invited user
      mockDb.user.findFirst.mockResolvedValue(null); // New user
      mockDb.role.findFirst.mockResolvedValue(mockRole as any);
      mockDb.workspaceInvite.findFirst.mockResolvedValue(null); // No existing invitation
      mockDb.workspaceInvite.create.mockResolvedValue(mockInvitation as any);

      const request = createMockRequest('POST', validInviteData, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data).toEqual({
        ...mockInvitation,
        createdAt: mockInvitation.createdAt.toISOString(),
        updatedAt: mockInvitation.updatedAt.toISOString(),
      });
      expect(mockDb.workspaceInvite.create).toHaveBeenCalledWith({
        data: {
          email: '<EMAIL>',
          workspaceId: 'workspace-1',
          invitedBy: 'user-1',
          userId: null,
          roleId: 'member',
          status: 'PENDING',
          token: expect.any(String),
        },
      });
    });

    it('should return 400 for invalid email', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(mockWorkspaceMembership as any);

      const request = createMockRequest('POST', { email: 'invalid-email', roleId: 'member' }, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 400 for missing roleId', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(mockWorkspaceMembership as any);

      const request = createMockRequest('POST', { email: '<EMAIL>' }, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 400 for invalid role', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(mockWorkspaceMembership as any);
      mockDb.role.findFirst.mockResolvedValue(null);

      const request = createMockRequest('POST', { email: '<EMAIL>', roleId: 'invalid-role' }, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 400 for existing invitation', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(mockWorkspaceMembership as any);
      mockDb.user.findFirst.mockResolvedValue(null);
      mockDb.role.findFirst.mockResolvedValue(mockRole as any);
      mockDb.workspaceInvite.findFirst.mockResolvedValue(mockInvitation as any); // Existing invitation

      const request = createMockRequest('POST', validInviteData, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(400);
    });

    it('should return 401 for unauthenticated user', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue(null as any);

      const request = createMockRequest('POST', validInviteData);
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(401);
    });

    it('should return 403 for user without permission', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: mockUser,
      } as any);
      mockDb.workspaceMembership.findFirst.mockResolvedValue(null);

      const request = createMockRequest('POST', validInviteData, 'valid-token');
      const response = await POST(request, { params: Promise.resolve({ workspaceId: 'workspace-1' }) });

      expect(response.status).toBe(403);
    });
  });
});
