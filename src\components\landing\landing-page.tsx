'use client';

import { useRouter } from '@/i18n/navigation';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Container from '@mui/material/Container';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { Brain } from '@phosphor-icons/react/dist/ssr/Brain';
import { ChartLineUp } from '@phosphor-icons/react/dist/ssr/ChartLineUp';
import { GithubLogo } from '@phosphor-icons/react/dist/ssr/GithubLogo';
import { Lightning } from '@phosphor-icons/react/dist/ssr/Lightning';
import { Plugs } from '@phosphor-icons/react/dist/ssr/Plugs';
import { Shield } from '@phosphor-icons/react/dist/ssr/Shield';
import { useTranslations } from 'next-intl';
import * as React from 'react';

import { DynamicLogo } from '@/components/core/logo';
import { useAuth } from '@/contexts/firebase-auth-context';
import { paths } from '@/paths';

export function LandingPage(): React.JSX.Element {
  const router = useRouter();
  const { user, loading } = useAuth();
  const t = useTranslations('landing');

  // Responsive breakpoints for performance optimization
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const _isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

  // Mobile menu state
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);

  // Performance optimization: Memoize expensive calculations
  const heroFontSize = React.useMemo(() => {
    if (isMobile) return '2.5rem';
    if (isTablet) return '4rem';
    return '5rem';
  }, [isMobile, isTablet]);

  const sectionPadding = React.useMemo(() => {
    return { xs: 8, md: 16 };
  }, []);

  const handleBookDemo = React.useCallback(() => {
    if (user) {
      router.push(paths.root);
    } else {
      router.push(paths.auth.signIn);
    }
  }, [user, router]);

  // Performance optimization: Memoize navigation handlers
  const handleMobileMenuToggle = React.useCallback(() => {
    setMobileMenuOpen(!mobileMenuOpen);
  }, [mobileMenuOpen]);

  const handleMobileMenuClose = React.useCallback(() => {
    setMobileMenuOpen(false);
  }, []);

  // Smooth scroll to sections
  const scrollToSection = React.useCallback((sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
    setMobileMenuOpen(false);
  }, []);

  // Performance optimization: Intersection Observer for animations
  const [visibleSections, setVisibleSections] = React.useState<Set<string>>(new Set());

  React.useEffect(() => {
    // Check if IntersectionObserver is available (browser environment)
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              setVisibleSections((prev) => new Set(prev).add(entry.target.id));
            }
          });
        },
        { threshold: 0.1, rootMargin: '50px' }
      );

      const sections = document.querySelectorAll('[id^="features"], [id^="integrations"], [id^="pricing"]');
      sections.forEach((section) => observer.observe(section));

      return () => observer.disconnect();
    } else {
      // Fallback for test environment - show all sections
      setVisibleSections(new Set(['features', 'integrations', 'pricing']));
    }
  }, []);

  // Performance optimization: Preload critical resources
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      // Preload critical CSS for faster rendering
      const preloadCSS = () => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = '/fonts/inter.css';
        document.head.appendChild(link);
      };

      // Optimize images with lazy loading
      const images = document.querySelectorAll('img[data-src]');
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement;
              img.src = img.dataset.src || '';
              img.classList.remove('lazy');
              imageObserver.unobserve(img);
            }
          });
        });
        images.forEach((img) => imageObserver.observe(img));
      }

      preloadCSS();

      // Performance monitoring
      if ('performance' in window && 'PerformanceObserver' in window) {
        try {
          const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
              if (entry.entryType === 'largest-contentful-paint') {
                console.log('LCP:', entry.startTime);
              }
            });
          });
          observer.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (error) {
          // Silently fail in test environment
          console.warn('Performance monitoring not available:', error);
        }
      }
    }
  }, []);

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          bgcolor: 'primary.dark',
        }}
      >
        <DynamicLogo height={60} width={60} />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        background: (muiTheme) =>
          `linear-gradient(135deg, ${muiTheme.palette.secondary.dark} 0%, ${muiTheme.palette.secondary.main} 50%, ${muiTheme.palette.secondary.dark} 100%)`,
        color: 'white',
      }}
    >
      {/* Header */}
      <Box
        component='header'
        sx={{
          py: 3,
          px: { xs: 2, md: 4 },
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <DynamicLogo height={40} width={40} />
          <Typography
            variant='h5'
            sx={{
              fontWeight: 700,
              color: 'white',
              letterSpacing: '-0.02em',
            }}
          >
            BSM Tech Pulse
          </Typography>
        </Box>
        {/* Mobile Menu Button */}
        <Box sx={{ display: { xs: 'flex', md: 'none' } }}>
          <Button
            variant='text'
            onClick={handleMobileMenuToggle}
            sx={{
              color: 'white',
              minWidth: 'auto',
              p: 1,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                gap: 0.5,
                '& > div': {
                  width: 24,
                  height: 2,
                  bgcolor: 'white',
                  borderRadius: 1,
                  transition: 'all 0.3s ease',
                  transformOrigin: 'center',
                },
                ...(mobileMenuOpen && {
                  '& > div:nth-of-type(1)': {
                    transform: 'rotate(45deg) translate(6px, 6px)',
                  },
                  '& > div:nth-of-type(2)': {
                    opacity: 0,
                  },
                  '& > div:nth-of-type(3)': {
                    transform: 'rotate(-45deg) translate(6px, -6px)',
                  },
                }),
              }}
            >
              <Box />
              <Box />
              <Box />
            </Box>
          </Button>
        </Box>

        {/* Desktop Navigation */}
        <Stack direction='row' spacing={4} sx={{ display: { xs: 'none', md: 'flex' } }}>
          <Button
            variant='text'
            onClick={() => scrollToSection('features')}
            sx={{
              color: 'rgba(255, 255, 255, 0.8)',
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '1rem',
              '&:hover': {
                color: 'white',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            {t('header.features')}
          </Button>
          <Button
            variant='text'
            onClick={() => scrollToSection('pricing')}
            sx={{
              color: 'rgba(255, 255, 255, 0.8)',
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '1rem',
              '&:hover': {
                color: 'white',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            {t('header.pricing')}
          </Button>
          <Button
            variant='text'
            onClick={() => scrollToSection('integrations')}
            sx={{
              color: 'rgba(255, 255, 255, 0.8)',
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '1rem',
              '&:hover': {
                color: 'white',
                bgcolor: 'rgba(255, 255, 255, 0.1)',
              },
            }}
          >
            {t('header.integrations')}
          </Button>
          <Button
            variant='contained'
            onClick={handleBookDemo}
            sx={{
              bgcolor: 'primary.main',
              color: 'white',
              borderRadius: 3,
              textTransform: 'none',
              fontWeight: 600,
              fontSize: '1rem',
              px: 4,
              py: 1.5,
              boxShadow: '0 8px 32px rgba(33, 150, 243, 0.3)',
              '&:hover': {
                bgcolor: 'primary.dark',
                boxShadow: '0 12px 40px rgba(33, 150, 243, 0.4)',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.3s ease',
            }}
          >
            {user ? t('hero.enterApp') : t('header.bookDemo')}
          </Button>
        </Stack>
      </Box>

      {/* Mobile Menu Overlay */}
      {mobileMenuOpen && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: 'rgba(26, 35, 126, 0.95)',
            backdropFilter: 'blur(20px)',
            zIndex: 1000,
            display: { xs: 'flex', md: 'none' },
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            animation: 'fadeIn 0.3s ease-out',
            '@keyframes fadeIn': {
              from: { opacity: 0 },
              to: { opacity: 1 },
            },
          }}
        >
          <Stack spacing={4} alignItems='center'>
            <Button
              variant='text'
              onClick={() => scrollToSection('features')}
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1.25rem',
                py: 2,
                '&:hover': {
                  color: 'white',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              {t('header.features')}
            </Button>
            <Button
              variant='text'
              onClick={() => scrollToSection('pricing')}
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1.25rem',
                py: 2,
                '&:hover': {
                  color: 'white',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              {t('header.pricing')}
            </Button>
            <Button
              variant='text'
              onClick={() => scrollToSection('integrations')}
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1.25rem',
                py: 2,
                '&:hover': {
                  color: 'white',
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              {t('header.integrations')}
            </Button>
            <Button
              variant='contained'
              onClick={() => {
                handleMobileMenuClose();
                handleBookDemo();
              }}
              sx={{
                bgcolor: 'primary.main',
                color: 'white',
                borderRadius: 3,
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '1.25rem',
                px: 6,
                py: 2,
                mt: 2,
                boxShadow: '0 8px 32px rgba(33, 150, 243, 0.3)',
                '&:hover': {
                  bgcolor: 'primary.dark',
                  boxShadow: '0 12px 40px rgba(33, 150, 243, 0.4)',
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.3s ease',
              }}
            >
              {user ? t('hero.enterApp') : t('header.bookDemo')}
            </Button>
          </Stack>
        </Box>
      )}

      {/* Hero Section */}
      <Container maxWidth='xl' sx={{ flex: 1, py: { xs: 6, md: 12 } }}>
        <Box sx={{ textAlign: 'center', mb: { xs: 8, md: 12 } }}>
          {/* Main Hero Content */}
          <Stack spacing={6} alignItems='center' sx={{ maxWidth: '1000px', mx: 'auto' }}>
            <Typography
              variant='h1'
              sx={{
                fontSize: heroFontSize,
                fontWeight: 800,
                lineHeight: { xs: 1.1, md: 1.05 },
                color: 'white',
                letterSpacing: '-0.02em',
                textAlign: 'center',
              }}
            >
              {t('hero.title')}{' '}
              <Box
                component='span'
                sx={{
                  background: (muiTheme) =>
                    `linear-gradient(135deg, ${muiTheme.palette.primary.light} 0%, ${muiTheme.palette.primary.main} 100%)`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  display: 'inline-block',
                }}
              >
                {t('hero.titleHighlight')}
              </Box>
            </Typography>

            <Typography
              variant='h5'
              sx={{
                fontWeight: 400,
                lineHeight: 1.6,
                color: 'rgba(255, 255, 255, 0.85)',
                maxWidth: '800px',
                fontSize: { xs: '1.25rem', md: '1.5rem' },
                textAlign: 'center',
              }}
            >
              {t('hero.subtitle')}
            </Typography>

            {/* CTA Buttons */}
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} sx={{ mt: 6 }}>
              <Button
                size='large'
                variant='contained'
                onClick={handleBookDemo}
                sx={{
                  py: 2,
                  px: 6,
                  borderRadius: 4,
                  textTransform: 'none',
                  fontWeight: 700,
                  fontSize: '1.125rem',
                  bgcolor: 'primary.main',
                  color: 'white',
                  boxShadow: '0 12px 40px rgba(33, 150, 243, 0.4)',
                  '&:hover': {
                    bgcolor: 'primary.dark',
                    boxShadow: '0 16px 48px rgba(33, 150, 243, 0.5)',
                    transform: 'translateY(-3px)',
                  },
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  willChange: 'transform, box-shadow',
                }}
              >
                {user ? t('hero.enterApp') : t('hero.bookDemo')}
              </Button>
              <Button
                size='large'
                variant='outlined'
                sx={{
                  py: 2,
                  px: 6,
                  borderRadius: 4,
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '1.125rem',
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                  color: 'white',
                  '&:hover': {
                    borderColor: 'rgba(255, 255, 255, 0.5)',
                    bgcolor: 'rgba(255, 255, 255, 0.1)',
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  willChange: 'transform, border-color',
                }}
              >
                {t('hero.ctaSecondary')}
              </Button>
            </Stack>
          </Stack>
        </Box>

        {/* Enhanced Product Mockup */}
        <Box sx={{ mt: { xs: 8, md: 16 }, maxWidth: '1200px', mx: 'auto' }}>
          <Box
            sx={{
              position: 'relative',
              borderRadius: 6,
              overflow: 'hidden',
              background: (muiTheme) =>
                `linear-gradient(135deg, ${muiTheme.palette.secondary.dark}40 0%, ${muiTheme.palette.primary.dark}20 100%)`,
              border: '1px solid rgba(255, 255, 255, 0.15)',
              p: { xs: 3, md: 4 },
              backdropFilter: 'blur(20px)',
              boxShadow: '0 24px 80px rgba(0, 0, 0, 0.3)',
            }}
          >
            {/* Mockup Header */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mb: 4,
                pb: 3,
                borderBottom: '1px solid rgba(255, 255, 255, 0.15)',
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <DynamicLogo height={32} width={32} />
                <Typography variant='h5' sx={{ color: 'white', fontWeight: 700 }}>
                  BSM Tech Pulse
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#ff5f57' }} />
                <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#ffbd2e' }} />
                <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#28ca42' }} />
              </Box>
            </Box>

            {/* Mobile Notification Style Alert */}
            <Box
              sx={{
                maxWidth: 400,
                mx: 'auto',
                mb: 4,
                bgcolor: 'rgba(255, 69, 58, 0.15)',
                border: '1px solid rgba(255, 69, 58, 0.4)',
                borderRadius: 3,
                p: 3,
                backdropFilter: 'blur(10px)',
                boxShadow: '0 8px 32px rgba(255, 69, 58, 0.2)',
                animation: 'slideIn 0.5s ease-out',
                '@keyframes slideIn': {
                  from: {
                    opacity: 0,
                    transform: 'translateY(-20px)',
                  },
                  to: {
                    opacity: 1,
                    transform: 'translateY(0)',
                  },
                },
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: 2,
                    bgcolor: 'rgba(255, 69, 58, 0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexShrink: 0,
                  }}
                >
                  <Lightning size={20} color='#ff453a' />
                </Box>
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography
                    variant='body2'
                    sx={{
                      color: 'rgba(255, 255, 255, 0.6)',
                      fontSize: '0.75rem',
                      mb: 0.5,
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px',
                    }}
                  >
                    BSM Tech Pulse • Now
                  </Typography>
                  <Typography
                    variant='body1'
                    sx={{
                      color: 'white',
                      fontWeight: 600,
                      mb: 1,
                      fontSize: '0.95rem',
                      lineHeight: 1.3,
                    }}
                  >
                    {t('mockup.projectTitle')}
                  </Typography>
                  <Typography
                    variant='body2'
                    sx={{
                      color: 'rgba(255, 255, 255, 0.8)',
                      fontSize: '0.85rem',
                      lineHeight: 1.4,
                    }}
                  >
                    {t('mockup.projectSubtitle')}
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Integration Icons */}
            <Stack direction='row' spacing={3} justifyContent='center' sx={{ opacity: 0.8 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                <GithubLogo size={32} color='white' />
                <Typography variant='caption' sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  GitHub
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                <Plugs size={32} color='white' />
                <Typography variant='caption' sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  Jira
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                <ChartLineUp size={32} color='white' />
                <Typography variant='caption' sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  Analytics
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                <Shield size={32} color='white' />
                <Typography variant='caption' sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  Security
                </Typography>
              </Box>
            </Stack>
          </Box>
        </Box>
      </Container>

      {/* Features Section */}
      <Box id='features' sx={{ py: sectionPadding, bgcolor: 'rgba(255, 255, 255, 0.02)' }}>
        <Container maxWidth='xl'>
          <Box sx={{ textAlign: 'center', mb: { xs: 8, md: 12 } }}>
            <Typography
              variant='h2'
              sx={{
                fontSize: { xs: '2rem', md: '3rem' },
                fontWeight: 700,
                color: 'white',
                mb: 3,
                letterSpacing: '-0.02em',
              }}
            >
              {t('features.title')}
            </Typography>
            <Typography
              variant='h6'
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                maxWidth: '600px',
                mx: 'auto',
                fontSize: { xs: '1.1rem', md: '1.25rem' },
                lineHeight: 1.6,
              }}
            >
              {t('features.subtitle')}
            </Typography>
          </Box>

          {/* Features Grid */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', md: '1fr 1fr' },
              gap: 4,
            }}
          >
            {/* AI Agents Feature */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
                transform: visibleSections.has('features') ? 'translateY(0)' : 'translateY(50px)',
                opacity: visibleSections.has('features') ? 1 : 0,
                willChange: 'transform, opacity',
                backfaceVisibility: 'hidden',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 3,
                      bgcolor: 'rgba(33, 150, 243, 0.2)',
                      border: '1px solid rgba(33, 150, 243, 0.3)',
                    }}
                  >
                    <Brain size={32} color='#2196f3' />
                  </Box>
                  <Typography variant='h5' sx={{ color: 'white', fontWeight: 700 }}>
                    {t('features.aiAgents.title')}
                  </Typography>
                </Box>
                <Typography
                  variant='body1'
                  sx={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: '1.1rem',
                    lineHeight: 1.7,
                  }}
                >
                  {t('features.aiAgents.description')}
                </Typography>
              </CardContent>
            </Card>

            {/* Integrations Feature */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 3,
                      bgcolor: 'rgba(33, 150, 243, 0.2)',
                      border: '1px solid rgba(33, 150, 243, 0.3)',
                    }}
                  >
                    <Plugs size={32} color='#2196f3' />
                  </Box>
                  <Typography variant='h5' sx={{ color: 'white', fontWeight: 700 }}>
                    {t('features.integrations.title')}
                  </Typography>
                </Box>
                <Typography
                  variant='body1'
                  sx={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: '1.1rem',
                    lineHeight: 1.7,
                  }}
                >
                  {t('features.integrations.description')}
                </Typography>
              </CardContent>
            </Card>

            {/* Insights Feature */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 3,
                      bgcolor: 'rgba(33, 150, 243, 0.2)',
                      border: '1px solid rgba(33, 150, 243, 0.3)',
                    }}
                  >
                    <ChartLineUp size={32} color='#2196f3' />
                  </Box>
                  <Typography variant='h5' sx={{ color: 'white', fontWeight: 700 }}>
                    {t('features.insights.title')}
                  </Typography>
                </Box>
                <Typography
                  variant='body1'
                  sx={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: '1.1rem',
                    lineHeight: 1.7,
                  }}
                >
                  {t('features.insights.description')}
                </Typography>
              </CardContent>
            </Card>

            {/* Security Feature */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 3,
                      bgcolor: 'rgba(33, 150, 243, 0.2)',
                      border: '1px solid rgba(33, 150, 243, 0.3)',
                    }}
                  >
                    <Shield size={32} color='#2196f3' />
                  </Box>
                  <Typography variant='h5' sx={{ color: 'white', fontWeight: 700 }}>
                    {t('features.security.title')}
                  </Typography>
                </Box>
                <Typography
                  variant='body1'
                  sx={{
                    color: 'rgba(255, 255, 255, 0.8)',
                    fontSize: '1.1rem',
                    lineHeight: 1.7,
                  }}
                >
                  {t('features.security.description')}
                </Typography>
              </CardContent>
            </Card>
          </Box>
        </Container>
      </Box>

      {/* Integration Showcase Section */}
      <Box id='integrations' sx={{ py: sectionPadding, bgcolor: 'rgba(255, 255, 255, 0.01)' }}>
        <Container maxWidth='xl'>
          {/* Section Header */}
          <Box sx={{ textAlign: 'center', mb: { xs: 8, md: 12 } }}>
            <Typography
              variant='h2'
              sx={{
                fontSize: { xs: '2rem', md: '3rem' },
                fontWeight: 700,
                color: 'white',
                mb: 3,
                letterSpacing: '-0.02em',
              }}
            >
              Seamless Integration Ecosystem
            </Typography>
            <Typography
              variant='h6'
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                maxWidth: '700px',
                mx: 'auto',
                fontSize: { xs: '1.1rem', md: '1.25rem' },
                lineHeight: 1.6,
              }}
            >
              Connect with 20+ development tools and platforms. One-click setup, real-time data sync, and
              enterprise-grade security.
            </Typography>
          </Box>

          {/* Main Integration Categories */}
          <Stack spacing={6}>
            {/* Development & Version Control */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                position: 'relative',
                overflow: 'hidden',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 16px 48px rgba(0, 0, 0, 0.2)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: 4,
                  background: 'linear-gradient(90deg, #2196f3 0%, #21cbf3 50%, #2196f3 100%)',
                },
              }}
            >
              <CardContent sx={{ p: { xs: 4, md: 6 } }}>
                <Box sx={{ mb: 4 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <Box
                      sx={{
                        p: 2,
                        borderRadius: 3,
                        bgcolor: 'rgba(33, 150, 243, 0.2)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      }}
                    >
                      <GithubLogo size={32} color='#2196f3' />
                    </Box>
                    <Typography variant='h4' sx={{ color: 'white', fontWeight: 700 }}>
                      Development & Version Control
                    </Typography>
                  </Box>
                  <Typography variant='body1' sx={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '1.1rem' }}>
                    Deep integration with your development workflow and version control systems
                  </Typography>
                </Box>
                <Stack direction={{ xs: 'column', md: 'row' }} spacing={4}>
                  {/* GitHub Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <GithubLogo size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        GitHub
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Pull requests, commits, issues, and repository analytics
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Real-time sync
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Webhooks
                      </Box>
                    </Box>
                  </Box>

                  {/* GitLab Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <GithubLogo size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        GitLab
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Merge requests, pipelines, and deployment tracking
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        CI/CD metrics
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        API integration
                      </Box>
                    </Box>
                  </Box>

                  {/* Bitbucket Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Shield size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Bitbucket
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Repository management and team collaboration
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Team insights
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Branch analysis
                      </Box>
                    </Box>
                  </Box>
                </Stack>
              </CardContent>
            </Card>

            {/* Project Management */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 16px 48px rgba(0, 0, 0, 0.2)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: { xs: 4, md: 6 } }}>
                <Box sx={{ mb: 4 }}>
                  <Typography variant='h4' sx={{ color: 'white', fontWeight: 700, mb: 2 }}>
                    Project Management & Planning
                  </Typography>
                  <Typography variant='body1' sx={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '1.1rem' }}>
                    Connect your project management tools for comprehensive delivery insights
                  </Typography>
                </Box>
                <Stack direction={{ xs: 'column', md: 'row' }} spacing={4}>
                  {/* Jira Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Plugs size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Jira
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Sprint planning, issue tracking, and velocity metrics
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Sprint analytics
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Burndown charts
                      </Box>
                    </Box>
                  </Box>

                  {/* Azure DevOps Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Lightning size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Azure DevOps
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Work items, boards, and release management
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Release tracking
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Work item flow
                      </Box>
                    </Box>
                  </Box>

                  {/* Linear Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <ChartLineUp size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Linear
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Modern issue tracking and project planning
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Cycle analytics
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Team velocity
                      </Box>
                    </Box>
                  </Box>
                </Stack>
              </CardContent>
            </Card>

            {/* Communication & Collaboration */}
            <Card
              sx={{
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 16px 48px rgba(0, 0, 0, 0.2)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: { xs: 4, md: 6 } }}>
                <Box sx={{ mb: 4 }}>
                  <Typography variant='h4' sx={{ color: 'white', fontWeight: 700, mb: 2 }}>
                    Communication & Collaboration
                  </Typography>
                  <Typography variant='body1' sx={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '1.1rem' }}>
                    Stay connected with your team through integrated communication platforms
                  </Typography>
                </Box>
                <Stack direction={{ xs: 'column', md: 'row' }} spacing={4}>
                  {/* Slack Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Brain size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Slack
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Automated notifications and team collaboration insights
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Smart alerts
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Custom reports
                      </Box>
                    </Box>
                  </Box>

                  {/* Microsoft Teams Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Shield size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Microsoft Teams
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Enterprise communication and meeting insights
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Meeting analytics
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Team insights
                      </Box>
                    </Box>
                  </Box>

                  {/* Discord Integration */}
                  <Box
                    sx={{
                      flex: 1,
                      p: 3,
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      borderRadius: 3,
                      bgcolor: 'rgba(255, 255, 255, 0.03)',
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        bgcolor: 'rgba(33, 150, 243, 0.1)',
                        border: '1px solid rgba(33, 150, 243, 0.3)',
                      },
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Brain size={32} color='#2196f3' />
                      <Typography variant='h6' sx={{ color: 'white', fontWeight: 600 }}>
                        Discord
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 2 }}>
                      Developer community and collaboration tracking
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Community insights
                      </Box>
                      <Box
                        sx={{
                          px: 2,
                          py: 0.5,
                          bgcolor: 'rgba(33, 150, 243, 0.2)',
                          borderRadius: 1,
                          fontSize: '0.75rem',
                          color: 'primary.light',
                        }}
                      >
                        Activity tracking
                      </Box>
                    </Box>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Stack>

          {/* Integration Benefits */}
          <Box sx={{ mt: { xs: 8, md: 12 }, textAlign: 'center' }}>
            <Typography variant='h4' sx={{ color: 'white', fontWeight: 700, mb: 4 }}>
              Enterprise-Grade Integration Benefits
            </Typography>
            <Stack direction={{ xs: 'column', md: 'row' }} spacing={4} justifyContent='center'>
              <Box sx={{ textAlign: 'center' }}>
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: 'rgba(33, 150, 243, 0.2)',
                    border: '2px solid rgba(33, 150, 243, 0.4)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 2,
                  }}
                >
                  <Lightning size={40} color='#2196f3' />
                </Box>
                <Typography variant='h6' sx={{ color: 'white', fontWeight: 600, mb: 1 }}>
                  One-Click Setup
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  Connect all your tools in minutes with our automated setup process
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'center' }}>
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: 'rgba(33, 150, 243, 0.2)',
                    border: '2px solid rgba(33, 150, 243, 0.4)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 2,
                  }}
                >
                  <Shield size={40} color='#2196f3' />
                </Box>
                <Typography variant='h6' sx={{ color: 'white', fontWeight: 600, mb: 1 }}>
                  Enterprise Security
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  SOC 2 compliant with end-to-end encryption and role-based access
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'center' }}>
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: 'rgba(33, 150, 243, 0.2)',
                    border: '2px solid rgba(33, 150, 243, 0.4)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 2,
                  }}
                >
                  <ChartLineUp size={40} color='#2196f3' />
                </Box>
                <Typography variant='h6' sx={{ color: 'white', fontWeight: 600, mb: 1 }}>
                  Real-Time Sync
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  Live data synchronization across all platforms for instant insights
                </Typography>
              </Box>
            </Stack>
          </Box>
        </Container>
      </Box>

      {/* Pricing Section */}
      <Box id='pricing' sx={{ py: sectionPadding, bgcolor: 'rgba(255, 255, 255, 0.02)' }}>
        <Container maxWidth='xl'>
          {/* Section Header */}
          <Box sx={{ textAlign: 'center', mb: { xs: 8, md: 12 } }}>
            <Typography
              variant='h2'
              sx={{
                fontSize: { xs: '2rem', md: '3rem' },
                fontWeight: 700,
                color: 'white',
                mb: 3,
                letterSpacing: '-0.02em',
              }}
            >
              {t('pricing.title')}
            </Typography>
            <Typography
              variant='h6'
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                maxWidth: '600px',
                mx: 'auto',
                fontSize: { xs: '1.1rem', md: '1.25rem' },
                lineHeight: 1.6,
              }}
            >
              {t('pricing.subtitle')}
            </Typography>
          </Box>

          {/* Pricing Cards */}
          <Stack direction={{ xs: 'column', lg: 'row' }} spacing={4} justifyContent='center'>
            {/* Team Manager Plan */}
            <Card
              sx={{
                maxWidth: 400,
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Typography variant='h5' sx={{ color: 'white', fontWeight: 700, mb: 1 }}>
                  {t('pricing.manager.title')}
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 4 }}>
                  {t('pricing.manager.description')}
                </Typography>
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant='h3'
                    sx={{
                      color: 'primary.light',
                      fontWeight: 800,
                      display: 'inline',
                    }}
                  >
                    {t('pricing.manager.price')}
                  </Typography>
                  <Typography
                    variant='body1'
                    sx={{
                      color: 'rgba(255, 255, 255, 0.7)',
                      display: 'inline',
                      ml: 1,
                    }}
                  >
                    {t('pricing.manager.period')}
                  </Typography>
                </Box>
                <Stack spacing={2} sx={{ mb: 4 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Team performance metrics
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Sprint analytics
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Developer productivity
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Integration management
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Standard support
                    </Typography>
                  </Box>
                </Stack>
                <Button
                  variant='outlined'
                  fullWidth
                  onClick={handleBookDemo}
                  sx={{
                    py: 2,
                    borderRadius: 3,
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: '1rem',
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                    color: 'white',
                    '&:hover': {
                      borderColor: 'primary.main',
                      bgcolor: 'rgba(33, 150, 243, 0.1)',
                    },
                  }}
                >
                  Get Started
                </Button>
              </CardContent>
            </Card>

            {/* Executive Suite Plan - Featured */}
            <Card
              sx={{
                maxWidth: 400,
                bgcolor: 'rgba(33, 150, 243, 0.1)',
                border: '2px solid',
                borderColor: 'primary.main',
                backdropFilter: 'blur(10px)',
                position: 'relative',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(33, 150, 243, 0.4)',
                },
              }}
            >
              {/* Popular Badge */}
              <Box
                sx={{
                  position: 'absolute',
                  top: -12,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  bgcolor: 'primary.main',
                  color: 'white',
                  px: 3,
                  py: 1,
                  borderRadius: 2,
                  fontSize: '0.875rem',
                  fontWeight: 600,
                }}
              >
                Most Popular
              </Box>
              <CardContent sx={{ p: 4, pt: 5 }}>
                <Typography variant='h5' sx={{ color: 'white', fontWeight: 700, mb: 1 }}>
                  {t('pricing.cto.title')}
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 4 }}>
                  {t('pricing.cto.description')}
                </Typography>
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant='h3'
                    sx={{
                      color: 'primary.light',
                      fontWeight: 800,
                      display: 'inline',
                    }}
                  >
                    {t('pricing.cto.price')}
                  </Typography>
                  <Typography
                    variant='body1'
                    sx={{
                      color: 'rgba(255, 255, 255, 0.7)',
                      display: 'inline',
                      ml: 1,
                    }}
                  >
                    {t('pricing.cto.period')}
                  </Typography>
                </Box>
                <Stack spacing={2} sx={{ mb: 4 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Strategic dashboard & KPIs
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Executive reporting suite
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Predictive analytics
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Multi-team insights
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Priority support
                    </Typography>
                  </Box>
                </Stack>
                <Button
                  variant='contained'
                  fullWidth
                  onClick={handleBookDemo}
                  sx={{
                    py: 2,
                    borderRadius: 3,
                    textTransform: 'none',
                    fontWeight: 700,
                    fontSize: '1rem',
                    bgcolor: 'primary.main',
                    color: 'white',
                    boxShadow: '0 8px 32px rgba(33, 150, 243, 0.4)',
                    '&:hover': {
                      bgcolor: 'primary.dark',
                      boxShadow: '0 12px 40px rgba(33, 150, 243, 0.5)',
                    },
                  }}
                >
                  Start Executive Trial
                </Button>
              </CardContent>
            </Card>

            {/* Enterprise Plan */}
            <Card
              sx={{
                maxWidth: 400,
                bgcolor: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(33, 150, 243, 0.3)',
                },
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Typography variant='h5' sx={{ color: 'white', fontWeight: 700, mb: 1 }}>
                  {t('pricing.enterprise.title')}
                </Typography>
                <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.7)', mb: 4 }}>
                  {t('pricing.enterprise.description')}
                </Typography>
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant='h3'
                    sx={{
                      color: 'primary.light',
                      fontWeight: 800,
                      display: 'inline',
                    }}
                  >
                    {t('pricing.enterprise.price')}
                  </Typography>
                  <Typography
                    variant='body1'
                    sx={{
                      color: 'rgba(255, 255, 255, 0.7)',
                      display: 'inline',
                      ml: 1,
                    }}
                  >
                    {t('pricing.enterprise.period')}
                  </Typography>
                </Box>
                <Stack spacing={2} sx={{ mb: 4 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Custom AI agents
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      White-label solution
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Dedicated success manager
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      Custom integrations
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Typography variant='caption' sx={{ color: 'white', fontSize: '0.75rem' }}>
                        ✓
                      </Typography>
                    </Box>
                    <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                      24/7 premium support
                    </Typography>
                  </Box>
                </Stack>
                <Button
                  variant='outlined'
                  fullWidth
                  onClick={handleBookDemo}
                  sx={{
                    py: 2,
                    borderRadius: 3,
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: '1rem',
                    borderColor: 'rgba(255, 255, 255, 0.3)',
                    color: 'white',
                    '&:hover': {
                      borderColor: 'primary.main',
                      bgcolor: 'rgba(33, 150, 243, 0.1)',
                    },
                  }}
                >
                  Contact Sales
                </Button>
              </CardContent>
            </Card>
          </Stack>
        </Container>
      </Box>

      {/* Footer */}
      <Box
        component='footer'
        sx={{
          py: 4,
          px: 3,
          mt: 'auto',
          borderTop: '1px solid rgba(255, 255, 255, 0.1)',
        }}
      >
        <Container maxWidth='lg'>
          <Stack direction={{ xs: 'column', sm: 'row' }} justifyContent='space-between' alignItems='center' spacing={2}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <DynamicLogo height={24} width={24} />
              <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.6)' }}>
                {t('footer.copyright')}
              </Typography>
            </Box>
            <Typography variant='body2' sx={{ color: 'rgba(255, 255, 255, 0.6)' }}>
              {t('footer.tagline')}
            </Typography>
          </Stack>
        </Container>
      </Box>
    </Box>
  );
}
