/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';

import { GET } from '../route';

// Mock dependencies
jest.mock('@/services/firebase/server-app', () => ({
  getAuthenticatedAppForUser: jest.fn(),
}));

jest.mock('@/services/db', () => ({
  db: {
    workspaceMembership: {
      findFirst: jest.fn(),
    },
    role: {
      findMany: jest.fn(),
    },
  },
}));

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

const mockGetAuthenticatedAppForUser = getAuthenticatedAppForUser as jest.MockedFunction<
  typeof getAuthenticatedAppForUser
>;
const mockDb = db as jest.Mocked<typeof db>;

describe('/api/workspace/[workspaceId]/roles', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET', () => {
    it('should return 401 when user is not authenticated', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/workspace/test-workspace/roles');
      const params = Promise.resolve({ workspaceId: 'test-workspace' });

      const response = await GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 403 when user is not a member of the workspace', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: { uid: 'user-123' },
      } as any);

      mockDb.workspaceMembership.findFirst.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/workspace/test-workspace/roles');
      const params = Promise.resolve({ workspaceId: 'test-workspace' });

      const response = await GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.error).toBe('You do not have access to this workspace');
    });

    it('should return workspace roles when user is a member', async () => {
      const mockRoles = [
        { id: 'owner', name: 'Owner', workspaceId: null },
        { id: 'member', name: 'Member', workspaceId: null },
        { id: 'custom-role', name: 'Custom Role', workspaceId: 'test-workspace' },
      ];

      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: { uid: 'user-123' },
      } as any);

      mockDb.workspaceMembership.findFirst.mockResolvedValue({
        userId: 'user-123',
        workspaceId: 'test-workspace',
        roleId: 'owner',
      } as any);

      mockDb.role.findMany.mockResolvedValue(mockRoles as any);

      const request = new NextRequest('http://localhost:3000/api/workspace/test-workspace/roles');
      const params = Promise.resolve({ workspaceId: 'test-workspace' });

      const response = await GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual(mockRoles);
      expect(mockDb.role.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { workspaceId: 'test-workspace' },
            { workspaceId: null },
          ],
        },
        select: {
          id: true,
          name: true,
          workspaceId: true,
        },
        orderBy: [
          { workspaceId: 'asc' },
          { name: 'asc' },
        ],
      });
    });

    it('should handle database errors gracefully', async () => {
      mockGetAuthenticatedAppForUser.mockResolvedValue({
        currentUser: { uid: 'user-123' },
      } as any);

      mockDb.workspaceMembership.findFirst.mockResolvedValue({
        userId: 'user-123',
        workspaceId: 'test-workspace',
        roleId: 'owner',
      } as any);

      mockDb.role.findMany.mockRejectedValue(new Error('Database error'));

      const request = new NextRequest('http://localhost:3000/api/workspace/test-workspace/roles');
      const params = Promise.resolve({ workspaceId: 'test-workspace' });

      const response = await GET(request, { params });
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to get workspace roles');
    });
  });
});
