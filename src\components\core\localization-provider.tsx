'use client';

import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon';
import { LocalizationProvider as Provider } from '@mui/x-date-pickers/LocalizationProvider';
import { useLocale, useTimeZone } from 'next-intl';
import * as React from 'react';

export interface LocalizationProviderProps {
  children: React.ReactNode;
}

export function LocalizationProvider({ children }: LocalizationProviderProps): React.JSX.Element {
  const locale = useLocale();

  const timezone = useTimeZone();

  console.log('[LocalizationProvider] Rendered with locale:', locale, 'timezone:', timezone);

  return (
    <Provider dateAdapter={AdapterLuxon} adapterLocale={locale}>
      {children}
    </Provider>
  );
}
