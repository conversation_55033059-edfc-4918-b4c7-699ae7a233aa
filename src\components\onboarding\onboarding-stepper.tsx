'use client';

import { useRouter } from '@/i18n/navigation';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CircularProgress from '@mui/material/CircularProgress';
import Container from '@mui/material/Container';
import MobileStepper from '@mui/material/MobileStepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import Stepper from '@mui/material/Stepper';
import { useColorScheme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import { ArrowLeftIcon } from '@phosphor-icons/react/dist/ssr/ArrowLeft';
import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr/ArrowRight';
import { useLocale, useTimeZone, useTranslations } from 'next-intl';
import * as React from 'react';

import { useCurrentUser } from '@/contexts/user-context';
import { useApiServices } from '@/hooks/use-api-services';
import { logger } from '@/lib/logger/default-logger';
import { getDefaultTimezone } from '@/lib/models/timezone';
import { paths } from '@/paths';

// Import step components
import { Language } from '@/lib/models/language';
import { Theme } from '@/lib/models/theme';
import { PreferencesStep } from './steps/preferences';
import { UserDetailsStep } from './steps/user-details';
import { WelcomeStep } from './steps/welcome';

// Define the steps
const steps = ['welcome', 'preferences', 'userDetails'];

export function OnboardingStepper(): React.JSX.Element {
  const t = useTranslations('onboarding');
  const router = useRouter();
  const locale = useLocale();
  const { mode } = useColorScheme();
  const { userApiService } = useApiServices();
  const { updateUser } = useCurrentUser();

  const timezone = useTimeZone();

  // Initialize active step from session storage if available
  const [activeStep, setActiveStep] = React.useState(() => {
    if (typeof window !== 'undefined') {
      const savedStep = sessionStorage.getItem('onboarding_step');
      return savedStep ? parseInt(savedStep, 10) : 0;
    }
    return 0;
  });

  // Initialize form data from session storage if available
  const [formData, setFormData] = React.useState(() => {
    const defaultFormData = {
      displayName: '',
      email: '',
      phone: '',
      country: 'BR',
      timezone: getDefaultTimezone().value,
      language: locale || Language.EN_US,
      theme: mode || Theme.system,
    };

    if (typeof window !== 'undefined') {
      const savedFormData = sessionStorage.getItem('onboarding_form_data');
      return savedFormData ? JSON.parse(savedFormData) : defaultFormData;
    }

    return defaultFormData;
  });

  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  // Save the current step to session storage whenever it changes
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('onboarding_step', activeStep.toString());
    }
  }, [activeStep]);

  // Save form data to session storage whenever it changes
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('onboarding_form_data', JSON.stringify(formData));
    }
  }, [formData]);

  // Handle form data changes
  const handleFormChange = (field: string, value: string) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  // References to step forms for validation
  const userDetailsFormRef = React.useRef<{ validate: () => Promise<boolean> }>(null);
  const preferencesFormRef = React.useRef<{ validate: () => Promise<boolean> }>(null);

  // Validate the current step using form refs
  const validateCurrentStep = async (): Promise<boolean> => {
    switch (activeStep) {
      case 0: // Welcome step - no validation needed
        return true;
      case 1: // Preferences step
        return preferencesFormRef.current?.validate() || false;
      case 2: // User details step
        return userDetailsFormRef.current?.validate() || false;
      default:
        return true;
    }
  };

  // Handle next step
  const handleNext = async () => {
    // Clear any previous errors
    setError(null);

    // Validate the current step before proceeding
    const isValid = await validateCurrentStep();
    if (!isValid) {
      setError(t('errors.requiredFields'));
      return;
    }

    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  // Handle back step
  const handleBack = () => {
    // Clear any previous errors when navigating
    setError(null);
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (activeStep !== steps.length - 1) {
      // Not on the last step yet
      await handleNext();
      return;
    }

    // Clear any previous errors
    setError(null);

    // Validate the current step before submitting
    const isValid = await validateCurrentStep();
    if (!isValid) {
      setError(t('errors.requiredFields'));
      return;
    }

    setLoading(true);
    try {
      // Submit the onboarding data
      const updatedUser = await userApiService.completeOnboarding({
        displayName: formData.displayName,
        country: formData.country,
        timezone: formData.timezone,
        phone: formData.phone,
        language: formData.language,
        theme: formData.theme as 'light' | 'dark' | 'system',
      });

      logger.debug('OnboardingStepper: Onboarding completed successfully', {
        userOnboarding: updatedUser.onboarding,
      });

      // Update the user context directly with the API response
      // This ensures the user context reflects the updated onboarding status immediately
      updateUser(updatedUser);

      // Clear session storage when onboarding is completed
      if (typeof window !== 'undefined') {
        sessionStorage.removeItem('onboarding_step');
        sessionStorage.removeItem('onboarding_form_data');
        sessionStorage.removeItem('onboarding_submitted');
      }

      logger.debug('OnboardingStepper: Redirecting to workspace selection');

      router.replace(paths.workspaceSelection);

      if (timezone !== formData.timezone) {
        // Timezone updates must have a reload to take effect on SSR
        setTimeout(() => {
          window.location.reload();
        }, 500);
      }
    } catch (err) {
      console.error('Error completing onboarding:', err);
      setError(t('errors.submitFailed'));
      setLoading(false);
    }
  };

  // Render the current step content
  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return <WelcomeStep />;
      case 1:
        return <PreferencesStep formData={formData} onChange={handleFormChange} ref={preferencesFormRef} />;
      case 2:
        return <UserDetailsStep formData={formData} onChange={handleFormChange} ref={userDetailsFormRef} />;
      default:
        return null;
    }
  };

  return (
    <Container maxWidth='md' sx={{ py: 4 }}>
      {/* Logout Button - Fixed position */}
      <Card>
        <CardContent>
          {/* Desktop Stepper */}
          <Box sx={{ display: { xs: 'none', md: 'block' }, mb: 4 }}>
            <Stepper activeStep={activeStep}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{t(`steps.${label}` as any)}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>
          {/* Mobile Stepper */}
          <Box sx={{ display: { xs: 'block', md: 'none' }, mb: 4 }}>
            <MobileStepper
              variant='dots'
              steps={steps.length}
              position='static'
              activeStep={activeStep}
              nextButton={<div />}
              backButton={<div />}
              sx={{ backgroundColor: 'transparent' }}
            />
          </Box>
          {/* Step Content */}
          <Box sx={{ mb: 4 }}>{renderStepContent()}</Box>
          {/* Error Message */}
          {error && (
            <Box sx={{ mb: 2 }}>
              <Typography color='error'>{error}</Typography>
            </Box>
          )}{' '}
          {/* Navigation Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            {/* Only show back button if not on first step */}
            {activeStep !== 0 && (
              <Button disabled={loading} onClick={handleBack} startIcon={<ArrowLeftIcon />}>
                {t('buttons.back')}
              </Button>
            )}
            {/* Empty div to maintain spacing when back button is hidden */}
            {activeStep === 0 && <div />}

            <Box>
              <Button
                variant='contained'
                color='primary'
                onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}
                disabled={loading}
                endIcon={loading ? <CircularProgress size={16} /> : <ArrowRightIcon />}
              >
                {activeStep === steps.length - 1
                  ? loading
                    ? t('buttons.saving')
                    : t('buttons.complete')
                  : t('buttons.next')}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
}
