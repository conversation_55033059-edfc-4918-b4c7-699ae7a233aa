import 'server-only';

import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/services/db';
import { getAuthenticatedAppForUser } from '@/services/firebase/server-app';

/**
 * @swagger
 * /api/workspace/{workspaceId}/members:
 *   get:
 *     summary: Get workspace members
 *     description: Retrieve all members of a specific workspace. Only workspace members can access this endpoint.
 *     tags:
 *       - Workspace
 *     parameters:
 *       - in: path
 *         name: workspaceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The workspace ID
 *     responses:
 *       200:
 *         description: Successfully retrieved workspace members
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   userId:
 *                     type: string
 *                     description: The user ID
 *                   workspaceId:
 *                     type: string
 *                     description: The workspace ID
 *                   roleId:
 *                     type: string
 *                     description: The role ID
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                     description: When the membership was created
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *                     description: When the membership was last updated
 *                   role:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                   user:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       email:
 *                         type: string
 *                       displayName:
 *                         type: string
 *                       avatar:
 *                         type: string
 *       401:
 *         description: Unauthorized - user not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized"
 *       403:
 *         description: Forbidden - user is not a member of the workspace
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "You do not have access to this workspace"
 *       404:
 *         description: Workspace not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Workspace not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to get workspace members"
 */
export async function GET(request: NextRequest, props: { params: Promise<{ workspaceId: string }> }) {
  const params = await props.params;
  try {
    const { workspaceId } = params;

    // Get the authenticated user from Firebase
    const authResult = await getAuthenticatedAppForUser();

    // If no user is authenticated, return 401
    if (!authResult?.currentUser) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const currentUser = authResult.currentUser;

    // Check if the user is a member of the workspace
    const userMembership = await db.workspaceMembership.findFirst({
      where: {
        userId: currentUser.uid,
        workspaceId: workspaceId,
      },
    });

    if (!userMembership) {
      return NextResponse.json({ error: 'You do not have access to this workspace' }, { status: 403 });
    }

    // Get all workspace members
    const members = await db.workspaceMembership.findMany({
      where: {
        workspaceId: workspaceId,
      },
      include: {
        role: {
          select: {
            id: true,
            name: true,
          },
        },
        user: {
          select: {
            id: true,
            email: true,
            displayName: true,
            avatar: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    return NextResponse.json(members);
  } catch (error) {
    console.error('Error getting workspace members:', error);
    return NextResponse.json({ error: 'Failed to get workspace members' }, { status: 500 });
  }
}
