{"settings": {"title": "Settings", "categories": {"personal": "Personal", "workspace": "Workspace"}, "tabs": {"account": "Account", "security": "Security", "notifications": "Notifications", "preferences": "Preferences", "billing": "Billing & plans", "team": "Team", "integrations": "Integrations"}, "preferences": {"title": "Preferences", "subheader": "Customize your experience", "language": "Language", "languageLabel": "Select language", "languageSwitch": "{locale, select, en_US {English} pt_BR {Português} es {Español} other {Unknown}}", "theme": "Theme", "themeOptions": {"light": "Light", "dark": "Dark", "system": "System"}, "preferences": {"title": "Set your preferences", "description": "Customize your experience with BMS Pulse."}}, "notifications": {"title": "Notifications", "subheader": "Manage the notifications", "email": "Email", "phone": "Phone", "productUpdates": "Product updates", "securityUpdates": "Security updates", "saveChanges": "Save changes"}, "password": {"title": "Password", "subheader": "Update password", "currentPassword": "Current password", "newPassword": "New password", "confirmPassword": "Confirm new password", "update": "Update", "currentPasswordRequired": "Current password is required", "newPasswordRequired": "New password is required", "confirmPasswordRequired": "Please confirm your new password", "passwordMismatch": "Passwords do not match", "minPasswordLength": "Password must be at least 6 characters", "successMessage": "Password updated successfully", "errors": {"updateFailed": "Failed to update password", "wrongPassword": "Current password is incorrect", "weakPassword": "New password is too weak", "requiresRecentLogin": "Please sign out and sign in again before changing your password"}}, "billing": {"title": "Billing & Plans", "subheader": "Manage your subscription and payment methods", "currentPlan": "Current Plan", "availablePlans": "Available Plans"}, "account": {"title": "Account", "basicDetails": {"title": "Basic details", "subheader": "Manage your personal information", "displayName": "Name", "email": "Email address", "phone": "Phone number", "countryLabel": "Country", "timezone": "Timezone", "language": "Language", "saveChanges": "Save changes", "countries": {"us": "United States", "br": "Brazil", "gb": "United Kingdom", "ca": "Canada", "jp": "Japan"}, "displayNameRequired": "The name is required", "invalidEmail": "Invalid email", "invalidPhone": "Invalid phone number", "countryRequired": "Invalid country", "timezoneRequired": "The timezone is invalid", "maxNameSize": "Name must be less than {size} characters", "avatarUploadError": "Error uploading avatar", "noChanges": "No changes performed", "profileUpdatedSuccess": "Profile updated successfully", "profileUpdateError": "Error updating user profile. Try again later"}, "profilePicture": {"remove": "Remove", "upload": "Upload", "uploadSuccess": "Profile picture updated successfully", "uploadError": "Failed to upload profile picture", "removeSuccess": "Profile picture removed successfully", "removeError": "Failed to remove profile picture", "fileTooLarge": "File size must be less than {size}MB", "invalidFileType": "Only PNG, JPEG, and JPG files are allowed", "confirmRemove": "Are you sure you want to remove your profile picture?", "cancel": "Cancel", "fileReadError": "Error reading file"}, "deleteAccount": {"title": "Delete account", "subheader": "Permanently delete your account and all of your content", "description": "This action cannot be undone. Once you delete your account, all of your content will be permanently removed.", "button": "Delete account"}}, "integrations": {"title": "Integrations", "loader": {"loadingText": "Loading integrations..."}, "tabs": {"all": "All", "installed": "Installed", "available": "Not Installed"}, "updated": "Updated", "installs": "installs", "search": "Search integration", "backToIntegrations": "Back to Integrations", "capabilitiesTitle": "Capabilities", "comingSoonMessage": "This integration is coming soon!", "notifyMessage": "We're working hard to bring this integration to you. Would you like to be notified when it's available?", "notifyButton": "Notify me", "noConfigAvailable": "No configuration available for this integration", "viewToggle": {"ariaLabel": "View mode", "card": "Card view", "table": "Table view"}, "status": {"available": "Available", "comingSoon": "Coming Soon", "installed": "Installed"}, "table": {"name": "Name", "description": "Description", "status": "Status", "actions": "Actions"}, "actions": {"add": "Add", "import": "Import", "export": "Export", "details": "Details", "install": "Install", "installing": "Installing...", "uninstall": "Uninstall", "uninstalling": "Uninstalling...", "save": "Save"}, "descriptions": {"github": "GitHub is a web-based hosting service for version control of code using Git.", "jira": "Jira is an issue tracking product developed by Atlassian that allows bug tracking and agile project management.", "slack": "Slack is a messaging app for business that connects people to the information they need.", "azuredevops": "Azure DevOps provides developer services for support teams to plan work, collaborate on code development, and build and deploy applications.", "bitbucket": "Bitbucket is a Git-based source code repository hosting service owned by Atlassian.", "gitlab": "GitLab is a web-based DevOps lifecycle tool that provides a Git repository manager providing wiki, issue-tracking and CI/CD pipeline features."}, "capabilities": {"pullRequests": "Pull/Merge request analytics", "codeReview": "Code review metrics", "commit": "Commit analysis", "repoInsights": "Repository insights", "teamPerformance": "Team performance tracking", "aiAnalytics": "AI tooling analytics", "issueTracking": "Issue tracking integration", "sprintPerformance": "Sprint performance metrics", "backlogAnalysis": "Backlog analysis", "projectTimeline": "Project timeline insights", "teamWorkload": "Team workload distribution", "teamCommunication": "Team communication analytics", "channelActivity": "Channel activity metrics", "notifications": "Notification integration", "alertDistribution": "Alert distribution", "collaborationInsights": "Collaboration insights", "workItems": "Work items tracking", "repositoryAnalytics": "Repository analytics", "cicdPipelines": "CI/CD pipeline analytics"}, "noResults": {"title": "No integrations found", "description": "No integrations match your search criteria. Try adjusting your search terms or browse all available integrations.", "clearSearch": "Clear search"}, "errorState": {"title": "Failed to load integrations", "description": "We couldn't load your workspace integrations. Please check your connection and try again.", "retry": "Retry"}, "refresh": "Refresh", "github": {"settings": "GitHub Settings", "syncFrequency": "Sync Frequency", "syncFrequencyHelp": "How often we should sync data from your GitHub repositories", "syncOptions": {"hourly": "Hourly", "daily": "Daily", "weekly": "Weekly"}, "webhookUrl": "Webhook URL", "webhookUrlHelp": "Use this URL in your GitHub App settings to receive webhook events", "dangerZone": "Danger Zone", "disconnectWarning": "Disconnecting will remove all GitHub data from your account. This action cannot be undone.", "disconnect": "Disconnect GitHub", "installations": "GitHub Installations", "refreshIntegrations": "Refresh integrations", "noIntegrations": "No GitHub organizations or users found. Connect your GitHub account to get started.", "installationsInfo": "GitHub Installations allow you to connect your GitHub organizations or personal accounts to BMS Pulse. This gives us access to your repositories metrics according to the permissions we need to provide you with the best insights!\n\nAll data is managed carefully and securely and we only have access to what we really need.", "installationDocs": "Learn more about GitHub App installations", "account": "Account", "type": "Type", "status": "Status", "connectedAt": "Connected At", "actions": "Actions", "organization": "Organization", "user": "User", "statusActive": "Active", "statusSuspended": "Suspended", "statusUninstalled": "Uninstalled", "statusSynchronizing": "Synchronizing", "suspendEntity": "Suspend", "resumeEntity": "Resume", "uninstallEntity": "Uninstall", "editEntity": "Edit", "confirmUninstallEntity": "Are you sure you want to uninstall this GitHub integration? This will remove all data associated with it.", "entitySuspended": "GitHub integration suspended successfully", "entityResumed": "GitHub integration resumed successfully", "entityUninstalled": "GitHub integration uninstalled successfully", "errorSuspendingEntity": "Failed to suspend GitHub integration", "errorReactivatingEntity": "Failed to resume GitHub integration", "errorUninstallingEntity": "Failed to uninstall GitHub integration", "errorLoadingDetails": "Failed to load GitHub integration details", "configSaved": "GitHub settings saved successfully", "errorSavingConfig": "Failed to save GitHub settings", "saving": "Saving...", "addInstallation": "Add Installation", "installationAdded": "GitHub installation added successfully", "errorAddingInstallation": "Failed to add GitHub installation", "installedCount": "{count, plural, =0 {No installations} =1 {1 installation} other {# installations}}", "action": {"title": "GitHub Installation", "subTitle": "This action will have impact on your GitHub integration for this workspace.", "confirmationInstruction": "To confirm, type the account name \"{accountName}\" below:", "executedSuccessfully": "Successfully executed GitHub installation action", "executedWithError": "Error executing GitHub installation action. Please try again later", "uninstalledDescription": "This will remove the integration with this GitHub account from this workspace and stop all data synchronization.", "suspendedDescription": "This will suspend the integration with this GitHub account for this workspace and stop all data synchronization.", "activeDescription": "This will resume the integration with this GitHub account for this workspace and restart all data synchronization.", "uninstalledWarning": "If this account is only connected to this workspace, it will also be uninstalled on GitHub. If it's linked to other BMS Pulse workspaces, it will remain active on GitHub.", "suspendedWarning": "If this account is only connected to this workspace, it will also be suspended on GitHub. If it's linked to other BMS Pulse workspaces, it will remain active on GitHub.", "activeWarning": "It will also be activated on GitHub if it is currently suspended. Other workspaces linked with same GitHub account will remain suspended.", "accountLoginMismatch": "Account name does not match", "cancelButton": "Cancel", "loading": "Loading...", "resumeButton": "Resume", "suspendButton": "Suspend", "uninstallButton": "Uninstall", "result": {"goToGitHubSettings": "Go to GitHub settings", "closeButton": "Close", "errorOnGitHub": "There was an error in the communication with GitHub API.", "uninstallNotExecutedOnChannel": "The installation has been successfully removed from this workspace.\n\nHowever, since other workspaces are using the same GitHub account, it remains linked to BMS Pulse.", "uninstalledOnGitHubErrorDescription": "The installation is no longer linked to this workspace, and we will no longer process any data for this integration.\n\nHowever, we encountered issues uninstalling it directly from GitHub. To fully remove BMS Pulse from your account, please visit GitHub and confirm that the uninstallation was successful.", "suspendNotExecutedOnChannel": "The installation has been successfully suspended in this workspace.\n\nHowever, since other workspaces are using the same GitHub account, it remains active on GitHub.", "suspendOnGitHubErrorDescription": "The installation has been suspended in this workspace, and we will no longer process any data for this integration.\n\nHowever, we encountered issues suspending it directly on GitHub. If you wish to suspend or remove it from GitHub as well, please visit GitHub directly and verify whether the action was successful.", "withOtherWorkspaces": "If you want this action to be reflected on GitHub and across all linked workspaces, you will need to either update the installation directly on GitHub or perform this action in each associated workspace.\n\nPlease note that this will affect the following workspaces:"}}}, "dangerZone": {"title": "Danger Zone", "description": "Actions in this section are irreversible and will permanently affect your integration.", "uninstallTitle": "Uninstall Integration", "uninstallDescription": "This will permanently remove this integration from your workspace and all connections linked to it.\nThe associated data will not be deleted immediately, but may be deleted after a while."}, "error": {"install": {"title": "Error installing integration", "description": "An error occurred while installing the extension. Please try again later."}}}, "team": {"title": "Team Members", "members": "Team Members", "pendingInvitations": "Pending Invitations", "noPendingInvitations": "No pending invitations", "inviteMember": "Invite Member", "name": "Name", "email": "Email", "role": "Role", "actions": "Actions", "edit": "Edit"}}, "git": {"overview": {"commitActivity": {"title": "Commit Activity", "today": "Today", "noActivity": "No activity", "sync": "Sync", "overview": "Overview"}, "actions": {"viewAll": "View all"}, "metrics": {"totalCommits": "Total Commits", "pullRequests": "Pull Requests", "velocity": "Velocity", "copilotMetrics": "Copilot Metrics"}, "timeframes": {"sinceLastMonth": "Since last month", "lastDays": "{days, plural, =1 {Last day} other {Last # days}}"}, "status": {"open": "Open", "merged": "<PERSON>rged", "closed": "Closed", "unknown": "Unknown"}, "tableHeaders": {"prNumber": "PR #", "author": "Author", "date": "Date", "status": "Status"}, "copilot": {"suggestions": "Suggestions", "acceptances": "Acceptances", "rejections": "Rejections"}, "git": {"lastCommit": "Last commit: {date, date, medium} {date, time, short}"}, "titles": {"recentPullRequests": "Recent Pull Requests", "recentRepositories": "Recent Repositories"}}, "repositories": {"title": "Repositories"}, "pullRequests": {"title": "Pull Requests"}, "commits": {"title": "Commits"}}, "customer": {"search": "Search customer"}, "userPopup": {"account": "Account", "security": "Security", "settings": "Settings", "signOut": "Sign out", "noDisplayName": "User"}, "insights": {"title": "Engineering Insights", "description": "Key metrics and answers to the most important questions for engineering managers.", "categories": {"deliveryDeadlines": "Delivery & Deadlines", "qualityRisk": "Quality & Risk", "teamHealth": "Team Health", "processEfficiency": "Process Efficiency", "businessValue": "Business Value", "benchmarksComparison": "Benchmarks & Comparison", "costResources": "Cost & Resources"}, "questions": {"sprintDeadline": "Will we hit the sprint / release date?", "blockingPRs": "Which PRs are blocking the deploy?", "pipelineBottleneck": "Which team / module is the pipeline bottleneck?", "rework": "How much rework (churn) did we have this week?", "bugRate": "Is bug rate rising after the latest merges?", "testCoverage": "How close are we to the test-coverage target?", "burnout": "Is anyone on the verge of burnout?", "workload": "Who is overloaded and who is under-utilized?", "reviewTime": "How much time do we spend just waiting for review?", "sprintImprovement": "Are we improving from sprint to sprint?", "techDebtVsFeatures": "How much effort went to features vs. tech debt?", "devROI": "Which initiatives bring the highest dev-ROI?", "marketComparison": "Are we above or below the market 50th percentile for cycle-time?", "internalBenchmark": "Which internal team should others mirror?", "costPerEpic": "What's the cost per epic per sprint and is it within budget?", "cicdLostHours": "How many CI/CD hours were lost due to failures?"}, "metrics": {"burndownForecast": "Burn-down forecast", "percentDone": "% done vs. planned", "openPRs": "PRs open > X h without review", "cycleTime": "Cycle-time by team & stage", "reworkPercentage": "% lines re-written ≤ 30 days", "incidentsPerKLOC": "Incidents per KLOC / per release", "coverageTarget": "Coverage vs. target", "consecutiveActiveHours": "Consecutive active hours", "offHoursCommits": "Off-hours commits", "workloadSpike": "Sudden workload spike", "wipPerDev": "WIP per dev", "storyPointDistribution": "Story-point distribution", "reviewLeadTime": "Review lead-time", "prIdleTime": "PR idle time", "deltaCycleTime": "Δ Cycle-time", "deltaLeadTime": "Δ Lead-time", "deltaDeployFreq": "Δ Deploy freq.", "investmentProfile": "Investment profile", "throughputVsOKR": "Throughput vs. OKR impact", "anonymisedBenchmark": "Anonymised benchmark", "internalHealthScore": "Internal Health-Score ranking", "costPerStoryPoint": "Cost per story-point", "buildFailMinutes": "Build-fail minutes", "pipelineMTTR": "Pipeline MTTR"}, "actions": {"rescope": "Rescope or add extra hands", "notifyReviewers": "Notify reviewers; auto-assign reviewer", "enablePairReview": "Enable pair review or parallel work", "startRootCause": "Start root cause analysis / refactor", "triggerQualityGate": "Trigger quality gate, expand tests", "createTestTasks": "Create tasks for critical tests", "schedule1on1": "Schedule 1-on-1, redistribute tasks", "rebalanceBacklog": "Rebalance backlog", "prSizePolicy": "PR-size policy, reviewer rotations", "adjustCeremonies": "Adjust ceremonies / WIP limit", "defendRefactoringTime": "Defend refactoring time", "prioritiseRoadmap": "Prioritise on roadmap", "shareBestPractices": "Share best practices or request coaching", "createGuilds": "Create guilds / learning dojos", "reviewEstimates": "Review estimates or hire", "automateRollback": "Automate rollback, optimise pipeline"}}, "common": {"title": "BMS Pulse"}, "errors": {"notFound": {"title": "404: The page you are looking for isn't here", "description": "You either tried some shady route or you came here by mistake. Whichever it is, try using the navigation", "action": "Go back to home", "altText": "Under development"}}, "auth": {"guard": {"loadUserError": "Failed to load user data", "checkOnboardingError": "Failed to check onboarding status", "retryingIn": "Retrying in {seconds} seconds...", "retryAttempt": "Retry attempt: {attempt} (Waiting {backoff} seconds)", "retryAttemptSimple": "Retry attempt: {attempt}", "authError": "Authentication error", "notFoundError": "Resource not found", "networkError": "Network connection error"}, "signIn": {"title": "Sign in", "emailLabel": "Email", "emailRequired": "Email is required", "passwordLabel": "Password", "passwordRequired": "Password is required", "submitButton": "Sign in", "noAccount": "Don't have an account?", "signUpLink": "Sign up", "forgotPassword": "Forgot password?"}, "signUp": {"title": "Sign up", "displayNameLabel": "Last name", "displayNameRequired": "Name is required", "emailLabel": "Email", "emailRequired": "Email is required", "passwordLabel": "Password", "passwordRequired": "Password should be at least 6 characters", "termsRequired": "You must accept the terms and conditions", "submitButton": "Sign up", "haveAccount": "Already have an account?", "signInLink": "Sign in"}, "resetPassword": {"title": "Reset password", "emailLabel": "Email", "emailRequired": "Email is required", "submitButton": "Send recovery link", "backToSignIn": "Back to Sign In", "rememberPassword": "Remember your password?", "signInLink": "Sign in", "successMessage": "Password reset email sent! Please check your inbox and follow the instructions."}, "errors": {"invalidCredentials": "Invalid email or password", "authFailed": "Authentication failed. Please try again.", "emailInUse": "This email is already in use. Please use a different email or sign in.", "weakPassword": "Password is too weak. Please use a stronger password.", "registrationFailed": "Registration failed. Please check your information and try again.", "resetFailed": "Failed to send password reset email", "userNotFound": "No account found with this email address", "invalidEmail": "Invalid email address format", "tooManyRequests": "Too many requests. Please try again later"}}, "nav": {"home": "Home", "insights": "Insights", "delivery": "Delivery & Deadlines", "quality": "Quality & Risk", "teamHealth": "Team Health", "process": "Process Efficiency", "business": "Business Value", "benchmarks": "Benchmarks", "cost": "Cost & Resources", "customers": "Customers", "integrations": "Integrations", "settings": "Settings", "account": "Account", "workspace": "Workspace", "search": "Search", "contacts": "Contacts", "notifications": "Notifications", "analytics": "Analytics", "git": "Git", "gitOverview": "Overview", "repositories": "Repositories", "pullRequests": "Pull Requests", "commits": "Commits"}, "layout": {"welcome": "Welcome to", "site": {"description": "Guiding startups to build scalable, resilient tech ecosystems through actionable strategies and real-world experience."}}, "onboarding": {"steps": {"welcome": "Welcome", "userDetails": "Your Details", "preferences": "Preferences"}, "buttons": {"back": "Back", "next": "Next", "complete": "Complete", "saving": "Saving...", "redirecting": "Redirecting...", "getStarted": "Get Started", "logout": "Sign Out"}, "errors": {"submitFailed": "Failed to complete onboarding. Please try again.", "requiredFields": "Please fill in all required fields before proceeding."}, "welcome": {"title": "Welcome to BMS Pulse", "description": "We're excited to have you on board! Let's set up your account to get the most out of BMS Pulse.", "imageAlt": "Welcome illustration"}, "userDetails": {"title": "Tell us about yourself", "description": "This information helps us personalize your experience.", "displayName": "Name", "email": "Email address", "phone": "Phone number (optional)", "invalidPhone": "Please enter a valid phone number", "countryLabel": "Country", "timezone": "Timezone", "displayNameRequired": "Name is required", "countries": {"us": "United States", "br": "Brazil", "gb": "United Kingdom", "ca": "Canada", "jp": "Japan"}, "countryRequired": "Country is required", "timezoneRequired": "Timezone is required", "maxNameSize": "Name must be less than {size} characters"}, "preferences": {"title": "Set your preferences", "description": "Customize your experience with BMS Pulse."}, "workspace": {"title": "Create your workspace", "description": "Your workspace is where you'll manage your projects and team.", "workspaceName": "Workspace name", "workspaceNamePlaceholder": "My Company", "workspaceNameRequired": "Workspace name is required", "workspaceNote": "You can add team members and configure your workspace later."}, "completion": {"title": "All set!", "description": "Your account is now ready to use. Next, you'll need to create or select a workspace to get started.", "imageAlt": "Setup complete illustration", "redirectMessage": "Redirecting to workspace selection in {seconds} seconds...", "processing": "Processing...", "redirecting": "Redirecting to workspace selection..."}}, "workspace": {"selection": {"title": "Select a workspace", "description": "Choose a workspace to continue, or create a new one.", "noWorkspaces": "No workspaces found", "noWorkspacesDescription": "You don't have access to any workspaces yet. Create your first workspace to get started.", "createWorkspace": "Create workspace", "selectWorkspace": "Select workspace", "workspaceName": "Workspace name", "workspaceNamePlaceholder": "My Company", "workspaceNameRequired": "Workspace name is required", "createButton": "Create", "cancelButton": "Cancel", "creating": "Creating...", "selecting": "Selecting...", "createWorkspaceTitle": "Create new workspace", "createWorkspaceDescription": "Enter a name for your new workspace.", "loadingWorkspaces": "Loading workspaces...", "searchWorkspaces": "Search workspaces...", "noSearchResults": "No workspaces found", "noSearchResultsDescription": "Try adjusting your search terms or create a new workspace.", "editWorkspace": "Edit Workspace", "deleteWorkspace": "Delete Workspace", "editWorkspaceTitle": "Edit Workspace", "editWorkspaceDescription": "Update your workspace details", "deleteWorkspaceTitle": "Delete Workspace", "deleteWorkspaceDescription": "This action cannot be undone", "workspaceAvatar": "Workspace Avatar", "uploadAvatarDescription": "Click to upload an avatar for your workspace", "avatarRequirements": "PNG, JPG up to 2MB", "changeAvatar": "Change Avatar", "saveChanges": "Save Changes", "saving": "Saving...", "deleting": "Deleting...", "deleteConfirmation": "Are you sure you want to delete", "deleteWarning": "All data associated with this workspace will be permanently deleted.", "deleteButton": "Delete", "createWorkspaceCardDescription": "Create a new workspace to get started", "errors": {"createFailed": "Could not create the workspace. Please try again later.", "selectFailed": "Could not select the workspace. Please try again later.", "loadFailed": "Could not load workspaces. Please try again later.", "editFailed": "Could not update the workspace. Please try again later.", "deleteFailed": "Could not delete the workspace. Please try again later."}, "invitationsCard": {"title": "Pending Invitations", "description": "You have {count, plural, =0 {no pending workspace invitations} =1 {1 pending workspace invitation} other {# pending workspace invitations}}", "viewInvitations": "View Invitations"}}, "invitations": {"title": "Invitations", "loading": "Loading invitations...", "noInvitations": "No pending invitations", "noInvitationsDescription": "You don't have any pending workspace invitations at the moment.", "invitedBy": "Invited by {name}", "invitedWithRole": "Role: {role}", "system": "system", "accept": "Accept", "decline": "Decline", "accepting": "Accepting...", "declining": "Declining...", "invitation": {"title": "Workspace Invitation", "error": "Invitation Error", "success": "Invitation Accepted"}, "pageTitle": "Workspace Invitation", "pageDescription": "Accept your workspace invitation", "description": "You have been invited to join these workspaces", "pending": "Pending", "labels": {"role": "Role", "invitedBy": "Invited by"}, "roles": "{role, select, owner {Owner} admin {Admin} member {Member} unknown {Unknown} other {Unknown}}", "messages": {"invitedTo": "You have been invited to join {workspace}", "signInRequired": "Please sign in or create an account to accept this invitation", "acceptedSuccess": "Successfully joined {workspace}!", "declinedSuccess": "Successfully declined invitation to {workspace}.", "redirecting": "Redirecting you to the workspace..."}, "actions": {"acceptInvitation": "Accept Invitation", "accepting": "Accepting...", "signIn": "Sign In", "signUp": "Sign Up", "goHome": "Go Home"}, "errors": {"notFound": "This invitation link is invalid or has expired", "alreadyProcessed": "This invitation has already been accepted or declined", "loadFailed": "Could not load invitation details. Please try again later.", "acceptFailed": "Could not accept the invitation. Please try again later.", "declineFailed": "Could not decline the invitation. Please try again later.", "listLoadFailed": "Could not load invitations. Please try again later.", "emailMismatch": "This invitation is for a different email address", "wrongAccount": "You are signed in as {currentEmail}, but this invitation is for {invitedEmail}. Please sign in with the correct account."}, "unified": {"sectionTitle": "Pending Invitations", "sectionDescription": "You have been invited to join these workspaces"}, "drawer": {"title": "Pending Invitations", "empty": "No pending invitations"}}}, "home": {"onboarding": {"welcome": {"title": "Welcome to BMS Pulse", "subtitle": "Your engineering analytics platform to help you make data-driven decisions and improve your development process."}, "getStarted": {"title": "Get Started with BMS Pulse", "subtitle": "Complete these steps to set up your workspace and start gaining insights"}, "steps": {"gitIntegration": {"title": "Connect Git Repository", "description": "Connect your GitHub, GitLab, or Bitbucket repositories to start tracking your development metrics."}, "exploreInsights": {"title": "Explore Insights", "description": "Discover actionable insights about your development process, team performance, and code quality."}, "customizeSettings": {"title": "Customize Your Experience", "description": "Set up your preferences, notifications, and team members to tailor BMS Pulse to your needs."}}, "quickStart": {"title": "Quick Start Guide", "subtitle": "Jump right in and explore the platform", "description": "BMS Pulse provides real-time analytics and insights for your development team. Start by exploring the Git Overview dashboard to see your team's activity at a glance.", "button": "Go to Dashboard", "imageAlt": "Dashboard preview"}, "common": {"getStarted": "Get Started", "learnMore": "Learn More", "skip": "Skip for now"}}}, "landing": {"header": {"termsOfService": "Terms of service", "privacyPolicy": "Privacy policy", "bookDemo": "Book a demo", "features": "Features", "pricing": "Pricing", "integrations": "Integrations"}, "badge": {"backedBy": "Backed by", "yCombinator": "Y Combinator", "trustedBy": ""}, "hero": {"title": "Engineering Clarity in", "titleHighlight": "One Pulse", "subtitle": "AI-powered agents tailor metrics and predictive insights for CTOs and engineering managers, turning raw developer data into decisive action.", "bookDemo": "Schedule Executive Demo", "signIn": "Sign In", "enterApp": "Enter Dashboard", "ctaSecondary": "View Live Demo", "metrics": {"teams": "", "insights": "", "roi": ""}}, "features": {"title": "AI-Powered Engineering Intelligence", "subtitle": "Purpose-built for executive decision making and strategic planning", "aiAgents": {"title": "Multi-Agent AI System", "description": "Specialized AI agents for different personas - CTOs get strategic insights, Engineering Managers get operational metrics"}, "integrations": {"title": "Enterprise Integration Ecosystem", "description": "Seamless integration with GitHub, Jira, Azure DevOps, Slack, and 20+ development tools"}, "insights": {"title": "Predictive Analytics & Forecasting", "description": "Advanced algorithms predict delivery timelines, identify bottlenecks, and recommend resource allocation"}, "security": {"title": "Enterprise-Grade Security", "description": "SOC 2 compliant with end-to-end encryption and role-based access controls"}}, "socialProof": {"title": "", "subtitle": "Experience the future of engineering analytics with BSM Tech Pulse", "testimonials": {"cto": {"quote": "", "name": "", "title": ""}, "vp": {"quote": "", "name": "", "title": ""}}}, "pricing": {"title": "Executive Pricing Plans", "subtitle": "Scalable solutions designed for growing engineering organizations", "cto": {"title": "Executive Suite", "price": "Contact Sales", "period": "", "description": "For CTOs and VP Engineering", "features": ["Strategic dashboard & KPIs", "Executive reporting suite", "Predictive analytics", "Multi-team insights", "Priority support"]}, "manager": {"title": "Team Manager", "price": "Contact Sales", "period": "", "description": "For Engineering Managers", "features": ["Team performance metrics", "Sprint analytics", "Developer productivity", "Integration management", "Standard support"]}, "enterprise": {"title": "Enterprise", "price": "Contact Sales", "period": "", "description": "For large organizations", "features": ["Custom AI agents", "White-label solution", "Dedicated success manager", "Custom integrations", "24/7 premium support"]}}, "mockup": {"projectTitle": "⚠️ Critical: Sprint velocity down 25%", "projectSubtitle": "AI detected bottleneck in code review process - immediate action required", "analyzingStack": "Analyzing development stack", "aiInsight": "AI Insight: Team Alpha shows 40% higher velocity on React projects"}, "footer": {"copyright": "© 2025 BSM Tech Pulse. All rights reserved.", "tagline": "Strategic Engineering Intelligence Platform", "company": "Company", "product": "Product", "resources": "Resources", "support": "Support"}}}