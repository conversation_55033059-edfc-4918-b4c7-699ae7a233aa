import * as React from 'react';

import { AuthenticatedUserGuard } from '@/components/auth/authenticated-user-guard';
import { WorkspaceProvider } from '@/contexts/workspace-context';

interface LayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: LayoutProps): React.JSX.Element {
  return (
    <AuthenticatedUserGuard>
      <WorkspaceProvider>{children}</WorkspaceProvider>
    </AuthenticatedUserGuard>
  );
}
