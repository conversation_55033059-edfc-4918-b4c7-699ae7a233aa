# Getting Started with BMS Tech Pulse

This guide will help you set up and run BMS Tech Pulse locally for development.

## Prerequisites

- <PERSON><PERSON> and Docker Compose
- Node.js (v16+)
- npm or yarn
- Git

## Setup Steps

### 1. Clone the Repository

```bash
git clone https://github.com/build-manage-scale/pulse.git
cd pulse
```

### 2. Environment Configuration

Create the environment file:

```bash
cp application/.env.example application/.env
```

Edit the `.env` file to configure your environment variables.

### 3. Start the Development Environment

Start all services using Docker Compose:

```bash
docker-compose up -d
```

This will start the following services:
- Application (Next.js) - http://localhost:3000
- TimescaleDB (PostgreSQL) - localhost:5432
- Redis - localhost:6379
- Nginx - http://localhost:80

### 4. Run Database Migrations

The database migrations are managed through Prisma ORM:

```bash
cd application
npx prisma migrate dev --name init
```

### 5. Start Development Server (Optional)

For local development outside of Docker:

```bash
cd application
npm install
npm run dev
```

## Development Workflow

### Application Development

The application is built with Next.js, TypeScript, and Material UI:

- **API Routes**: Define API endpoints in `application/src/app/api/`
- **Pages**: Create new pages in `application/src/app/`
- **Components**: Add reusable UI components in `application/src/components/`
- **State Management**: Use contexts in `application/src/contexts/`
- **Hooks**: Create custom hooks in `application/src/hooks/`

### Database Changes

When making changes to the database schema:

1. Update the Prisma schema in `application/src/lib/database/schema.prisma`
2. Create migrations with `npx prisma migrate dev --name your-migration-name`
3. Update TimescaleDB specific configurations in `database/init.sql` if needed

## Testing

Run tests with:

```bash
cd application
npm run test
```

## Deployment

See [Firebase Deployment Guide](./firebase-deployment.md) for instructions on deploying the application to Firebase App Hosting.