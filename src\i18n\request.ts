import { hasLocale, IntlErrorCode } from 'next-intl';
import { getRequestConfig } from 'next-intl/server';

import { logger } from '@/lib/logger/default-logger';

import { getCurrentTimeZone } from '@/lib/server/timezone';
import { formats } from './formats';
import { routing } from './routing';

export default getRequestConfig(async ({ requestLocale }) => {
  // Typically corresponds to the `[locale]` segment
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested) ? requested : routing.defaultLocale;

  const timeZone = await getCurrentTimeZone();

  return {
    locale,
    timeZone,
    formats,
    messages: (await import(`../../messages/${locale}.json`)).default,
    onError(error) {
      if (error.code === IntlErrorCode.MISSING_MESSAGE) {
        logger.warn('Translation warning:', error);
      } else {
        logger.error('Translation error:', error);
      }
    },
  };
});
