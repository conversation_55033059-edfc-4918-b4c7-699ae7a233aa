import crypto from 'crypto';

/**
 * Generate a secure random token for invitations
 * @param length The length of the token (default: 32)
 * @returns A secure random token string
 */
export function generateInvitationToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Generate a URL-safe secure random token for invitations
 * @param length The length of the token (default: 32)
 * @returns A URL-safe secure random token string
 */
export function generateUrlSafeInvitationToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('base64url');
}

/**
 * Validate if a token has the expected format
 * @param token The token to validate
 * @param expectedLength The expected length of the token (default: 32)
 * @returns True if the token is valid, false otherwise
 */
export function isValidInvitationToken(token: string, expectedLength: number = 32): boolean {
  if (!token || typeof token !== 'string') {
    return false;
  }

  // For hex tokens, length should be expectedLength * 2
  if (token.match(/^[a-f0-9]+$/i)) {
    return token.length === expectedLength * 2;
  }

  // For base64url tokens, length varies but should be reasonable
  if (token.match(/^[A-Za-z0-9_-]+$/)) {
    return token.length >= 32 && token.length <= 64;
  }

  return false;
}

/**
 * Hash a token for secure storage (optional, for additional security)
 * @param token The token to hash
 * @returns The hashed token
 */
export function hashToken(token: string): string {
  return crypto.createHash('sha256').update(token).digest('hex');
}
